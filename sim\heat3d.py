"""
3D Heat Equation Solver for NeoPhysics.

Implements explicit and implicit finite difference methods for heat diffusion.
"""

import numpy as np
from typing import Tuple, Optional, Dict, Any
from scipy.sparse import diags
from scipy.sparse.linalg import spsolve
import warnings

class Heat3DSolver:
    """3D heat equation solver using finite differences."""
    
    def __init__(self, grid_size: Tuple[int, int, int], grid_spacing: float = 0.1,
                 thermal_diffusivity: float = 1.0, method: str = "explicit"):
        """
        Initialize the 3D heat solver.
        
        Args:
            grid_size: (nx, ny, nz) grid dimensions
            grid_spacing: Spatial step size (dx = dy = dz)
            thermal_diffusivity: Thermal diffusivity (alpha = k/(rho*cp))
            method: "explicit" or "implicit"
        """
        self.nx, self.ny, self.nz = grid_size
        self.dx = grid_spacing
        self.alpha = thermal_diffusivity
        self.method = method
        
        # Stability condition for explicit method
        self.dt_max = self.dx**2 / (6 * self.alpha)  # 3D stability limit
        self.dt = 0.8 * self.dt_max  # Use 80% of max for safety
        
        # Initialize temperature field
        self.T = np.zeros((self.nx, self.ny, self.nz), dtype=np.float32)
        self.T_new = np.zeros_like(self.T)
        
        # Boundary conditions and source terms
        self.boundary_mask = np.zeros_like(self.T, dtype=bool)
        self.boundary_values = np.zeros_like(self.T)
        self.heat_sources = np.zeros_like(self.T)
        
        # Material properties (can vary spatially)
        self.thermal_conductivity = np.full_like(self.T, 1.0)
        self.density = np.full_like(self.T, 1.0)
        self.specific_heat = np.full_like(self.T, 1.0)
        
        # Precompute coefficients for implicit method
        if self.method == "implicit":
            self._setup_implicit_matrix()
    
    def set_initial_temperature(self, temperature: np.ndarray):
        """Set the initial temperature field."""
        if temperature.shape != (self.nx, self.ny, self.nz):
            raise ValueError(f"Temperature shape {temperature.shape} doesn't match grid {(self.nx, self.ny, self.nz)}")
        self.T = temperature.astype(np.float32)
    
    def set_boundary_condition(self, mask: np.ndarray, values: np.ndarray):
        """Set Dirichlet boundary conditions."""
        self.boundary_mask = mask.astype(bool)
        self.boundary_values = values.astype(np.float32)
    
    def add_heat_source(self, source: np.ndarray):
        """Add heat source term (W/m³)."""
        self.heat_sources += source.astype(np.float32)
    
    def add_sphere_source(self, center: Tuple[float, float, float], radius: float, 
                         power: float):
        """Add a spherical heat source."""
        cx, cy, cz = center
        
        # Convert to grid indices
        ix = int(cx / self.dx)
        iy = int(cy / self.dx)
        iz = int(cz / self.dx)
        
        # Create spherical mask
        x = np.arange(self.nx) * self.dx
        y = np.arange(self.ny) * self.dx
        z = np.arange(self.nz) * self.dx
        
        X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
        distance = np.sqrt((X - cx)**2 + (Y - cy)**2 + (Z - cz)**2)
        
        # Gaussian distribution for smooth source
        sigma = radius / 3.0  # 3-sigma rule
        source = power * np.exp(-0.5 * (distance / sigma)**2)
        source = source / (sigma * np.sqrt(2 * np.pi))**3  # Normalize
        
        self.add_heat_source(source)
    
    def add_object_boundary(self, center: Tuple[float, float, float], size: float,
                           temperature: float, object_type: str = "cube"):
        """Add boundary conditions for an object."""
        cx, cy, cz = center
        
        x = np.arange(self.nx) * self.dx
        y = np.arange(self.ny) * self.dx
        z = np.arange(self.nz) * self.dx
        
        X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
        
        if object_type == "cube":
            mask = ((np.abs(X - cx) <= size/2) & 
                   (np.abs(Y - cy) <= size/2) & 
                   (np.abs(Z - cz) <= size/2))
        elif object_type == "sphere":
            distance = np.sqrt((X - cx)**2 + (Y - cy)**2 + (Z - cz)**2)
            mask = distance <= size/2
        else:
            raise ValueError(f"Unknown object type: {object_type}")
        
        # Set boundary conditions
        self.boundary_mask |= mask
        self.boundary_values[mask] = temperature
    
    def _setup_implicit_matrix(self):
        """Setup the coefficient matrix for implicit method."""
        # This is a simplified version - full implementation would be more complex
        # For now, we'll use explicit method as fallback
        pass
    
    def step_explicit(self, dt: Optional[float] = None) -> float:
        """Perform one explicit time step."""
        if dt is None:
            dt = self.dt
        
        # Check stability
        if dt > self.dt_max:
            warnings.warn(f"Time step {dt} exceeds stability limit {self.dt_max}")
            dt = self.dt_max
        
        # Compute thermal diffusivity field
        alpha_field = self.thermal_conductivity / (self.density * self.specific_heat)
        
        # Finite difference coefficients
        r = alpha_field * dt / (self.dx**2)
        
        # Interior points using vectorized operations
        self.T_new[1:-1, 1:-1, 1:-1] = (
            self.T[1:-1, 1:-1, 1:-1] * (1 - 6*r[1:-1, 1:-1, 1:-1]) +
            r[1:-1, 1:-1, 1:-1] * (
                self.T[2:, 1:-1, 1:-1] + self.T[:-2, 1:-1, 1:-1] +  # x-direction
                self.T[1:-1, 2:, 1:-1] + self.T[1:-1, :-2, 1:-1] +  # y-direction
                self.T[1:-1, 1:-1, 2:] + self.T[1:-1, 1:-1, :-2]   # z-direction
            ) +
            self.heat_sources[1:-1, 1:-1, 1:-1] * dt / (self.density[1:-1, 1:-1, 1:-1] * self.specific_heat[1:-1, 1:-1, 1:-1])
        )
        
        # Handle boundaries (zero-gradient by default)
        self.T_new[0, :, :] = self.T_new[1, :, :]
        self.T_new[-1, :, :] = self.T_new[-2, :, :]
        self.T_new[:, 0, :] = self.T_new[:, 1, :]
        self.T_new[:, -1, :] = self.T_new[:, -2, :]
        self.T_new[:, :, 0] = self.T_new[:, :, 1]
        self.T_new[:, :, -1] = self.T_new[:, :, -2]
        
        # Apply Dirichlet boundary conditions
        self.T_new[self.boundary_mask] = self.boundary_values[self.boundary_mask]
        
        # Swap arrays
        self.T, self.T_new = self.T_new, self.T
        
        return dt
    
    def step_implicit(self, dt: Optional[float] = None) -> float:
        """Perform one implicit time step."""
        # For now, fallback to explicit method
        # Full implicit implementation would require solving a large sparse system
        return self.step_explicit(dt)
    
    def step(self, dt: Optional[float] = None) -> float:
        """Perform one time step using the selected method."""
        if self.method == "explicit":
            return self.step_explicit(dt)
        elif self.method == "implicit":
            return self.step_implicit(dt)
        else:
            raise ValueError(f"Unknown method: {self.method}")
    
    def simulate(self, total_time: float, dt: Optional[float] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Run simulation for a given time period.
        
        Returns:
            times: Array of time points
            temperatures: Array of temperature fields at each time point
        """
        if dt is None:
            dt = self.dt
        
        n_steps = int(total_time / dt)
        times = np.zeros(n_steps + 1)
        temperatures = np.zeros((n_steps + 1, self.nx, self.ny, self.nz))
        
        # Store initial state
        times[0] = 0.0
        temperatures[0] = self.T.copy()
        
        # Run simulation
        current_time = 0.0
        for i in range(n_steps):
            actual_dt = self.step(dt)
            current_time += actual_dt
            times[i + 1] = current_time
            temperatures[i + 1] = self.T.copy()
        
        return times, temperatures
    
    def get_temperature_field(self) -> np.ndarray:
        """Get the current temperature field."""
        return self.T.copy()
    
    def get_stats(self) -> Dict[str, float]:
        """Get statistics about the current temperature field."""
        return {
            'min_temp': float(np.min(self.T)),
            'max_temp': float(np.max(self.T)),
            'mean_temp': float(np.mean(self.T)),
            'total_energy': float(np.sum(self.T * self.density * self.specific_heat) * self.dx**3)
        }
    
    def reset(self):
        """Reset the solver to initial conditions."""
        self.T.fill(0.0)
        self.T_new.fill(0.0)
        self.heat_sources.fill(0.0)
        self.boundary_mask.fill(False)
        self.boundary_values.fill(0.0)