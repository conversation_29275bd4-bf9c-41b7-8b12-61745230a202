#!/usr/bin/env python3
"""
Test rigid body physics for NeoPhysics.

Tests ball dropping with gravity and real-time visualization.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_ball_physics():
    """Test ball dropping physics."""
    print("Testing NeoPhysics rigid body physics...")
    
    # Create scene and executor
    scene = Scene("Physics Test Scene")
    executor = ActionExecutor(scene)
    
    # Test commands for rigid body physics
    commands = [
        "create a ball 2m high",
        "run physics"
    ]
    
    for i, command in enumerate(commands, 1):
        print(f"\n{i}. Command: '{command}'")
        
        try:
            # Parse command
            actions = parse_command(command)
            print(f"   Parsed {len(actions)} actions")
            
            # Execute actions
            for action in actions:
                result = executor.execute(action)
                if result["success"]:
                    print(f"   [OK] {result['message']}")
                else:
                    print(f"   [ERROR] {result['error']}")
        
        except Exception as e:
            print(f"   [ERROR] Error: {e}")
    
    # Test physics stepping
    print(f"\nTesting physics simulation steps...")
    
    rigid_solver = executor.get_rigid_solver()
    if rigid_solver:
        print(f"Initial state:")
        for body in rigid_solver.bodies.values():
            print(f"  {body.name}: pos={body.position}, vel={body.velocity}")
        
        # Step physics a few times
        for step in range(5):
            result = executor.step_physics(1.0/60.0)  # 60 FPS
            if result["success"]:
                stats = result.get("stats", {})
                print(f"Step {step+1}: time={stats.get('time', 0):.3f}s, energy={stats.get('total_energy', 0):.2f}J")
                
                # Show ball position
                for body in rigid_solver.bodies.values():
                    print(f"  {body.name}: y={body.position[1]:.3f}m, vy={body.velocity[1]:.3f}m/s")
    
    # Print final scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Physics bodies: {len(rigid_solver.bodies) if rigid_solver else 0}")
    
    print("\nPhysics test completed!")

if __name__ == "__main__":
    test_ball_physics()