#!/usr/bin/env python3
"""
Simple test for the enhanced rigid body system to isolate issues.
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sim.rigid3d import RigidBodySolver

def test_simple_step():
    """Test a simple simulation step."""
    print("Testing simple simulation step...")
    
    solver = RigidBodySolver(gravity=9.81)
    
    # Create one sphere
    sphere = solver.create_sphere_body(
        body_id="sphere1",
        name="Sphere 1",
        position=np.array([0.0, 3.0, 0.0]),
        radius=0.3,
        mass=1.0
    )
    
    print(f"Created sphere at: {sphere.position}")
    print(f"Frame count: {solver.frame_count}")
    
    # Try one step
    try:
        solver.step(dt=1.0/60.0)
        print(f"Step completed. Frame count: {solver.frame_count}")
        print(f"Sphere position: {sphere.position}")
        return True
    except Exception as e:
        print(f"Error during step: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_simple_step():
        print("Simple test passed!")
    else:
        print("Simple test failed!")
