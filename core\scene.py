"""
Scene management for NeoPhysics.

Handles the scene graph, objects, materials, and physics state.
"""

import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import uuid

class ObjectType(Enum):
    """Types of objects that can exist in the scene."""
    CUBE = "cube"
    SPHERE = "sphere"
    PLANE = "plane"
    FIELD = "field"

class MaterialType(Enum):
    """Material types for physics simulation."""
    THERMAL = "thermal"
    FLUID = "fluid"
    RIGID = "rigid"

@dataclass
class Material:
    """Material properties for physics simulation."""
    name: str
    material_type: MaterialType
    properties: Dict[str, float] = field(default_factory=dict)
    
    @classmethod
    def thermal(cls, name: str, conductivity: float = 1.0, density: float = 1.0, 
                specific_heat: float = 1.0) -> 'Material':
        """Create a thermal material."""
        return cls(
            name=name,
            material_type=MaterialType.THERMAL,
            properties={
                'thermal_conductivity': conductivity,
                'density': density,
                'specific_heat': specific_heat
            }
        )

@dataclass
class Transform:
    """3D transformation (position, rotation, scale)."""
    position: np.ndarray = field(default_factory=lambda: np.array([0.0, 0.0, 0.0]))
    rotation: np.ndarray = field(default_factory=lambda: np.array([0.0, 0.0, 0.0]))  # Euler angles
    scale: np.ndarray = field(default_factory=lambda: np.array([1.0, 1.0, 1.0]))

@dataclass
class SceneObject:
    """A 3D object in the scene."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    object_type: ObjectType = ObjectType.CUBE
    transform: Transform = field(default_factory=Transform)
    material: Optional[Material] = None
    properties: Dict[str, Any] = field(default_factory=dict)
    visible: bool = True
    
    def __post_init__(self):
        if not self.name:
            self.name = f"{self.object_type.value}_{self.id[:8]}"

@dataclass
class FieldData:
    """3D field data (temperature, velocity, pressure, etc.)."""
    name: str
    data: np.ndarray  # 3D or 4D array (x, y, z) or (x, y, z, components)
    grid_spacing: float = 1.0
    origin: np.ndarray = field(default_factory=lambda: np.array([0.0, 0.0, 0.0]))
    units: str = ""
    
    @property
    def shape(self) -> Tuple[int, ...]:
        return self.data.shape
    
    @property
    def is_vector_field(self) -> bool:
        return len(self.data.shape) == 4

class Scene:
    """Main scene container for NeoPhysics."""
    
    def __init__(self, name: str = "Untitled Scene"):
        self.name = name
        self.objects: Dict[str, SceneObject] = {}
        self.fields: Dict[str, FieldData] = {}
        self.materials: Dict[str, Material] = {}
        self.time: float = 0.0
        self.dt: float = 0.01  # Default timestep
        
        # Physics simulation state
        self.is_simulating: bool = False
        self.simulation_step: int = 0
        
        # Default materials
        self._create_default_materials()
    
    def _create_default_materials(self):
        """Create default materials."""
        self.materials["default_thermal"] = Material.thermal("Default Thermal")
        self.materials["copper"] = Material.thermal("Copper", conductivity=400.0)
        self.materials["steel"] = Material.thermal("Steel", conductivity=50.0)
        self.materials["insulator"] = Material.thermal("Insulator", conductivity=0.1)
    
    def add_object(self, obj: SceneObject) -> str:
        """Add an object to the scene."""
        self.objects[obj.id] = obj
        return obj.id
    
    def remove_object(self, object_id: str) -> bool:
        """Remove an object from the scene."""
        if object_id in self.objects:
            del self.objects[object_id]
            return True
        return False
    
    def get_object(self, object_id: str) -> Optional[SceneObject]:
        """Get an object by ID."""
        return self.objects.get(object_id)
    
    def get_object_by_name(self, name: str) -> Optional[SceneObject]:
        """Get an object by name."""
        for obj in self.objects.values():
            if obj.name == name:
                return obj
        return None
    
    def add_field(self, field: FieldData) -> None:
        """Add a field to the scene."""
        self.fields[field.name] = field
    
    def get_field(self, name: str) -> Optional[FieldData]:
        """Get a field by name."""
        return self.fields.get(name)
    
    def create_cube(self, name: str = "", position: Tuple[float, float, float] = (0, 0, 0),
                   size: float = 1.0, material: str = "default_thermal",
                   temperature: float = 20.0) -> str:
        """Create a cube object."""
        transform = Transform(position=np.array(position), scale=np.array([size, size, size]))
        
        obj = SceneObject(
            name=name,
            object_type=ObjectType.CUBE,
            transform=transform,
            material=self.materials.get(material),
            properties={'temperature': temperature}
        )
        
        return self.add_object(obj)
    
    def create_sphere(self, name: str = "", position: Tuple[float, float, float] = (0, 0, 0),
                     radius: float = 0.5, material: str = "default_thermal",
                     temperature: float = 20.0) -> str:
        """Create a sphere object."""
        transform = Transform(position=np.array(position), scale=np.array([radius, radius, radius]))
        
        obj = SceneObject(
            name=name,
            object_type=ObjectType.SPHERE,
            transform=transform,
            material=self.materials.get(material),
            properties={'temperature': temperature, 'radius': radius}
        )
        
        return self.add_object(obj)
    
    def create_temperature_field(self, name: str = "temperature", 
                                grid_size: Tuple[int, int, int] = (32, 32, 32),
                                grid_spacing: float = 0.1,
                                initial_temp: float = 20.0) -> None:
        """Create a temperature field."""
        data = np.full(grid_size, initial_temp, dtype=np.float32)
        
        field = FieldData(
            name=name,
            data=data,
            grid_spacing=grid_spacing,
            units="Celsius"
        )
        
        self.add_field(field)
    
    def clear(self):
        """Clear the scene."""
        self.objects.clear()
        self.fields.clear()
        self.time = 0.0
        self.simulation_step = 0
        self.is_simulating = False
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the scene."""
        return {
            'name': self.name,
            'num_objects': len(self.objects),
            'num_fields': len(self.fields),
            'time': self.time,
            'simulation_step': self.simulation_step,
            'is_simulating': self.is_simulating,
            'objects': [
                {
                    'id': obj.id,
                    'name': obj.name,
                    'type': obj.object_type.value,
                    'position': obj.transform.position.tolist(),
                    'material': obj.material.name if obj.material else None
                }
                for obj in self.objects.values()
            ],
            'fields': [
                {
                    'name': field.name,
                    'shape': field.shape,
                    'units': field.units
                }
                for field in self.fields.values()
            ]
        }