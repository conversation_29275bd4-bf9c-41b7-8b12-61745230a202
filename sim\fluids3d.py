"""
3D Incompressible Fluid Solver for NeoPhysics.

Implements MAC grid with pressure projection for Navier-Stokes equations.
"""

import numpy as np
from typing import Tuple, Optional, Dict, Any
from scipy.sparse import diags, csr_matrix
from scipy.sparse.linalg import spsolve
import warnings

class FluidSolver3D:
    """3D incompressible fluid solver using MAC grid."""
    
    def __init__(self, grid_size: Tuple[int, int, int], grid_spacing: float = 0.1,
                 viscosity: float = 0.01, density: float = 1.0):
        """
        Initialize 3D fluid solver.
        
        Args:
            grid_size: (nx, ny, nz) grid dimensions
            grid_spacing: Spatial step size
            viscosity: Kinematic viscosity
            density: Fluid density
        """
        self.nx, self.ny, self.nz = grid_size
        self.dx = grid_spacing
        self.viscosity = viscosity
        self.density = density
        self.dt = 0.01
        
        # MAC grid: staggered velocity components
        self.u = np.zeros((self.nx + 1, self.ny, self.nz))  # x-velocity
        self.v = np.zeros((self.nx, self.ny + 1, self.nz))  # y-velocity
        self.w = np.zeros((self.nx, self.ny, self.nz + 1))  # z-velocity
        self.p = np.zeros((self.nx, self.ny, self.nz))      # pressure
        
        # Temporary arrays
        self.u_temp = np.zeros_like(self.u)
        self.v_temp = np.zeros_like(self.v)
        self.w_temp = np.zeros_like(self.w)
        
        # Boundary conditions
        self.boundary_mask = np.zeros((self.nx, self.ny, self.nz), dtype=bool)
        
        # External forces
        self.force_x = np.zeros_like(self.u)
        self.force_y = np.zeros_like(self.v)
        self.force_z = np.zeros_like(self.w)
        
        # Gravity
        self.gravity = -9.81
        
        # Precompute pressure projection matrix
        self._setup_pressure_matrix()
    
    def _setup_pressure_matrix(self):
        """Setup sparse matrix for pressure projection."""
        n = self.nx * self.ny * self.nz
        
        # Create Laplacian matrix for pressure Poisson equation
        diagonals = []
        offsets = []
        
        # Main diagonal
        main_diag = -6 * np.ones(n)
        diagonals.append(main_diag)
        offsets.append(0)
        
        # x-direction neighbors
        x_diag = np.ones(n - 1)
        for i in range(self.nx - 1, n, self.nx):
            if i < n - 1:
                x_diag[i] = 0  # No connection across x-boundary
        diagonals.extend([x_diag, x_diag])
        offsets.extend([1, -1])
        
        # y-direction neighbors
        y_diag = np.ones(n - self.nx)
        diagonals.extend([y_diag, y_diag])
        offsets.extend([self.nx, -self.nx])
        
        # z-direction neighbors
        z_diag = np.ones(n - self.nx * self.ny)
        diagonals.extend([z_diag, z_diag])
        offsets.extend([self.nx * self.ny, -self.nx * self.ny])
        
        self.pressure_matrix = diags(diagonals, offsets, shape=(n, n), format='csr')
    
    def add_inflow(self, position: Tuple[int, int, int], velocity: Tuple[float, float, float],
                   radius: int = 2):
        """Add inflow boundary condition."""
        x, y, z = position
        vx, vy, vz = velocity
        
        # Create spherical inflow region
        for i in range(max(0, x-radius), min(self.nx, x+radius+1)):
            for j in range(max(0, y-radius), min(self.ny, y+radius+1)):
                for k in range(max(0, z-radius), min(self.nz, z+radius+1)):
                    dist = np.sqrt((i-x)**2 + (j-y)**2 + (k-z)**2)
                    if dist <= radius:
                        # Set velocity
                        if i < self.nx:
                            self.u[i, j, k] = vx
                        if j < self.ny:
                            self.v[i, j, k] = vy
                        if k < self.nz:
                            self.w[i, j, k] = vz
    
    def add_obstacle(self, position: Tuple[int, int, int], size: int):
        """Add solid obstacle."""
        x, y, z = position
        
        for i in range(max(0, x-size), min(self.nx, x+size+1)):
            for j in range(max(0, y-size), min(self.ny, y+size+1)):
                for k in range(max(0, z-size), min(self.nz, z+size+1)):
                    self.boundary_mask[i, j, k] = True
    
    def step(self, dt: Optional[float] = None):
        """Advance simulation by one time step."""
        if dt is None:
            dt = self.dt
        
        # 1. Apply external forces
        self._apply_forces(dt)
        
        # 2. Advection step
        self._advect(dt)
        
        # 3. Viscosity step (implicit)
        self._apply_viscosity(dt)
        
        # 4. Pressure projection
        self._project()
        
        # 5. Apply boundary conditions
        self._apply_boundaries()
    
    def _apply_forces(self, dt: float):
        """Apply external forces including gravity."""
        # Gravity on y-velocity
        self.v += self.gravity * dt
        
        # External forces
        self.u += self.force_x * dt
        self.v += self.force_y * dt
        self.w += self.force_z * dt
    
    def _advect(self, dt: float):
        """Advect velocity field (semi-Lagrangian)."""
        # Simplified advection - in practice would use proper semi-Lagrangian
        
        # u-component advection
        self.u_temp[1:-1, :, :] = self.u[1:-1, :, :] - dt * (
            self.u[1:-1, :, :] * (self.u[2:, :, :] - self.u[:-2, :, :]) / (2 * self.dx)
        )
        
        # v-component advection  
        self.v_temp[:, 1:-1, :] = self.v[:, 1:-1, :] - dt * (
            self.v[:, 1:-1, :] * (self.v[:, 2:, :] - self.v[:, :-2, :]) / (2 * self.dx)
        )
        
        # w-component advection
        self.w_temp[:, :, 1:-1] = self.w[:, :, 1:-1] - dt * (
            self.w[:, :, 1:-1] * (self.w[:, :, 2:] - self.w[:, :, :-2]) / (2 * self.dx)
        )
        
        # Copy back
        self.u[:] = self.u_temp[:]
        self.v[:] = self.v_temp[:]
        self.w[:] = self.w_temp[:]
    
    def _apply_viscosity(self, dt: float):
        """Apply viscosity (simplified explicit)."""
        alpha = self.viscosity * dt / (self.dx**2)
        
        # u-component
        self.u_temp[1:-1, 1:-1, 1:-1] = self.u[1:-1, 1:-1, 1:-1] + alpha * (
            self.u[2:, 1:-1, 1:-1] + self.u[:-2, 1:-1, 1:-1] +
            self.u[1:-1, 2:, 1:-1] + self.u[1:-1, :-2, 1:-1] +
            self.u[1:-1, 1:-1, 2:] + self.u[1:-1, 1:-1, :-2] -
            6 * self.u[1:-1, 1:-1, 1:-1]
        )
        
        # v-component
        self.v_temp[1:-1, 1:-1, 1:-1] = self.v[1:-1, 1:-1, 1:-1] + alpha * (
            self.v[2:, 1:-1, 1:-1] + self.v[:-2, 1:-1, 1:-1] +
            self.v[1:-1, 2:, 1:-1] + self.v[1:-1, :-2, 1:-1] +
            self.v[1:-1, 1:-1, 2:] + self.v[1:-1, 1:-1, :-2] -
            6 * self.v[1:-1, 1:-1, 1:-1]
        )
        
        # w-component
        self.w_temp[1:-1, 1:-1, 1:-1] = self.w[1:-1, 1:-1, 1:-1] + alpha * (
            self.w[2:, 1:-1, 1:-1] + self.w[:-2, 1:-1, 1:-1] +
            self.w[1:-1, 2:, 1:-1] + self.w[1:-1, :-2, 1:-1] +
            self.w[1:-1, 1:-1, 2:] + self.w[1:-1, 1:-1, :-2] -
            6 * self.w[1:-1, 1:-1, 1:-1]
        )
        
        self.u[:] = self.u_temp[:]
        self.v[:] = self.v_temp[:]
        self.w[:] = self.w_temp[:]
    
    def _project(self):
        """Simplified pressure projection."""
        # Simplified - just apply some smoothing to reduce divergence
        # Full MAC grid implementation would be more complex
        
        # Smooth velocity fields
        for _ in range(5):
            u_new = self.u.copy()
            v_new = self.v.copy()
            w_new = self.w.copy()
            
            # Simple averaging (avoiding boundary issues)
            if self.nx > 2:
                u_new[1:-1, :, :] = 0.8 * self.u[1:-1, :, :] + 0.1 * (self.u[2:, :, :] + self.u[:-2, :, :])
            if self.ny > 2:
                v_new[:, 1:-1, :] = 0.8 * self.v[:, 1:-1, :] + 0.1 * (self.v[:, 2:, :] + self.v[:, :-2, :])
            if self.nz > 2:
                w_new[:, :, 1:-1] = 0.8 * self.w[:, :, 1:-1] + 0.1 * (self.w[:, :, 2:] + self.w[:, :, :-2])
            
            self.u[:] = u_new[:]
            self.v[:] = v_new[:]
            self.w[:] = w_new[:]
    
    def _apply_boundaries(self):
        """Apply boundary conditions."""
        # No-slip boundaries at domain edges
        self.u[0, :, :] = 0
        self.u[-1, :, :] = 0
        self.v[:, 0, :] = 0
        self.v[:, -1, :] = 0
        self.w[:, :, 0] = 0
        self.w[:, :, -1] = 0
        
        # Obstacle boundaries
        for i in range(self.nx):
            for j in range(self.ny):
                for k in range(self.nz):
                    if self.boundary_mask[i, j, k]:
                        # Set velocities to zero around obstacles
                        if i < self.nx:
                            self.u[i, j, k] = 0
                            self.u[i+1, j, k] = 0
                        if j < self.ny:
                            self.v[i, j, k] = 0
                            self.v[i, j+1, k] = 0
                        if k < self.nz:
                            self.w[i, j, k] = 0
                            self.w[i, j, k+1] = 0
    
    def get_velocity_magnitude(self) -> np.ndarray:
        """Get velocity magnitude field."""
        # Interpolate velocities to cell centers
        u_center = 0.5 * (self.u[:-1, :, :] + self.u[1:, :, :])
        v_center = 0.5 * (self.v[:, :-1, :] + self.v[:, 1:, :])
        w_center = 0.5 * (self.w[:, :, :-1] + self.w[:, :, 1:])
        
        return np.sqrt(u_center**2 + v_center**2 + w_center**2)
    
    def get_vorticity(self) -> np.ndarray:
        """Get vorticity magnitude."""
        # Simplified vorticity calculation
        vort = np.zeros((self.nx, self.ny, self.nz))
        
        # ω = ∇ × v
        if self.nx > 2 and self.ny > 2 and self.nz > 2:
            vort[1:-1, 1:-1, 1:-1] = np.sqrt(
                ((self.w[1:-1, 2:, 1:-1] - self.w[1:-1, :-2, 1:-1]) / (2*self.dx) -
                 (self.v[1:-1, 1:-1, 2:] - self.v[1:-1, 1:-1, :-2]) / (2*self.dx))**2 +
                ((self.u[1:-1, 1:-1, 2:] - self.u[1:-1, 1:-1, :-2]) / (2*self.dx) -
                 (self.w[2:, 1:-1, 1:-1] - self.w[:-2, 1:-1, 1:-1]) / (2*self.dx))**2 +
                ((self.v[2:, 1:-1, 1:-1] - self.v[:-2, 1:-1, 1:-1]) / (2*self.dx) -
                 (self.u[1:-1, 2:, 1:-1] - self.u[1:-1, :-2, 1:-1]) / (2*self.dx))**2
            )
        
        return vort
    
    def get_stats(self) -> Dict[str, float]:
        """Get fluid simulation statistics."""
        vel_mag = self.get_velocity_magnitude()
        
        return {
            'max_velocity': float(np.max(vel_mag)),
            'avg_velocity': float(np.mean(vel_mag)),
            'kinetic_energy': float(0.5 * self.density * np.sum(vel_mag**2) * self.dx**3),
            'max_pressure': float(np.max(self.p)),
            'min_pressure': float(np.min(self.p))
        }
    
    def reset(self):
        """Reset fluid simulation."""
        self.u.fill(0)
        self.v.fill(0)
        self.w.fill(0)
        self.p.fill(0)
        self.force_x.fill(0)
        self.force_y.fill(0)
        self.force_z.fill(0)