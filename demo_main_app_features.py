#!/usr/bin/env python3
"""
Demonstration of enhanced physics features available through the main application.

This script shows how users can now access the high-fidelity rigid body physics
through natural language commands in the main NeoPhysics application.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def demo_main_app_features():
    """Demonstrate enhanced features available in main app."""
    print("NeoPhysics Enhanced Features Demo")
    print("=" * 50)
    print("These commands are now available in the main application!")
    print()
    
    # Create the same setup as the main app
    scene = Scene("Enhanced Physics Demo")
    executor = ActionExecutor(scene)
    
    # Demonstrate enhanced commands
    enhanced_commands = [
        # Enhanced physics objects with proper inertia tensors
        "Create a physics sphere at position 0,3,0 with radius 0.4 and mass 2.0",
        "Add a physics box at position 2,2,0 with dimensions 1.0,0.5,0.8 and mass 1.5",
        
        # Traditional commands still work (backward compatibility)
        "Create a ball 4m high with mass 0.5",
        
        # Physics simulation with enhanced features
        "Run physics simulation"
    ]
    
    print("Available Enhanced Commands:")
    print("-" * 30)
    
    for i, command in enumerate(enhanced_commands, 1):
        print(f"{i}. '{command}'")
        
        # Parse and execute
        actions = parse_command(command)
        for action in actions:
            result = executor.execute(action)
            if result["success"]:
                print(f"   ✓ {result.get('message', 'Success')}")
            else:
                print(f"   ✗ {result.get('error', 'Failed')}")
        print()
    
    # Show the enhanced physics system in action
    print("Enhanced Physics System Status:")
    print("-" * 35)
    
    if executor.rigid_solver:
        stats = executor.rigid_solver.get_stats()
        print(f"• Total bodies: {stats['num_bodies']}")
        print(f"• Total energy: {stats['total_energy']:.2f} J")
        print(f"• Contact manifolds: {stats['num_manifolds']}")
        print(f"• Active contacts: {stats['num_contacts']}")
        print()
        
        print("Body Details (Enhanced Features):")
        for body in executor.rigid_solver.bodies.values():
            print(f"• {body.name}:")
            print(f"  - Geometry: {body.collision_geometry.geometry_type.value}")
            print(f"  - Mass: {body.mass:.2f} kg")
            print(f"  - Inertia tensor diagonal: {body.inertia_tensor.tensor.diagonal()}")
            print(f"  - Position: [{body.position[0]:.2f}, {body.position[1]:.2f}, {body.position[2]:.2f}]")
            
            if hasattr(body.collision_geometry, 'radius'):
                print(f"  - Radius: {body.collision_geometry.radius:.2f} m")
            elif hasattr(body.collision_geometry, 'half_extents'):
                dims = body.collision_geometry.half_extents * 2
                print(f"  - Dimensions: [{dims[0]:.2f}, {dims[1]:.2f}, {dims[2]:.2f}] m")
            print()
    
    # Run a few simulation steps to show the enhanced physics
    print("Running Enhanced Physics Simulation:")
    print("-" * 40)
    print("Step  Time   Energy   Contacts  Bodies")
    
    for step in range(30):
        result = executor.step_physics(dt=1.0/60.0)
        if result["success"] and step % 5 == 0:
            stats = result["stats"]
            print(f"{step:4d}  {stats['time']:5.2f}  {stats['total_energy']:7.2f}  "
                  f"{stats['num_contacts']:8d}  {stats['num_bodies']:6d}")
    
    print()
    print("🎉 Enhanced Features Summary:")
    print("=" * 50)
    print("✓ Proper 3x3 inertia tensors for realistic rotation")
    print("✓ Multiple geometry types (spheres, boxes)")
    print("✓ Contact manifold generation and persistence")
    print("✓ Constraint-based collision resolution")
    print("✓ Symplectic semi-implicit integration")
    print("✓ Enhanced energy and momentum conservation")
    print("✓ Backward compatibility with existing commands")
    print("✓ Natural language interface for all features")
    print()
    print("These features are now available in 'python main.py'!")

def show_command_examples():
    """Show examples of commands users can try."""
    print("\n" + "=" * 60)
    print("COMMAND EXAMPLES FOR MAIN APPLICATION")
    print("=" * 60)
    print()
    print("Try these commands in the NeoPhysics command panel:")
    print()
    
    examples = [
        ("Basic Physics Objects", [
            "Create a physics sphere at position 0,2,0 with radius 0.3",
            "Add a physics box at position 1,3,0 with dimensions 0.8,0.6,1.0",
            "Create a ball 5m high with mass 2kg"
        ]),
        
        ("Advanced Properties", [
            "Create a physics sphere with mass 1.5 and radius 0.4",
            "Add a physics box with dimensions 1.2,0.8,0.6 and mass 3.0",
            "Create a ball with mass 0.5kg at position 2,4,0"
        ]),
        
        ("Simulation Control", [
            "Run physics simulation",
            "Set gravity to 3.71",  # Mars gravity
            "Set velocity of ball to 5,0,0",
            "Clear the scene"
        ])
    ]
    
    for category, commands in examples:
        print(f"{category}:")
        for cmd in commands:
            print(f"  • \"{cmd}\"")
        print()
    
    print("Key Improvements Over Basic Version:")
    print("• Realistic rotational dynamics with proper inertia")
    print("• Better collision detection and response")
    print("• Energy conservation and stability")
    print("• Support for different object shapes")
    print("• Enhanced physics statistics and monitoring")

if __name__ == "__main__":
    demo_main_app_features()
    show_command_examples()
