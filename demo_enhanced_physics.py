#!/usr/bin/env python3
"""
Comprehensive demonstration of the enhanced rigid body physics system.

This demo showcases the new high-fidelity features:
- Proper inertia tensors for different shapes
- Contact manifold generation and persistence
- Constraint-based collision resolution
- Symplectic integration
- Multiple geometry types (spheres and boxes)
- Enhanced statistics and energy conservation
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sim.rigid3d import RigidBodySolver, BodyType

def demo_enhanced_physics():
    """Demonstrate the enhanced physics system."""
    print("Enhanced Rigid Body Physics Demo")
    print("=" * 50)
    
    # Create solver with Earth gravity
    solver = RigidBodySolver(gravity=9.81)
    
    print("Creating rigid bodies with proper geometry and inertia...")
    
    # Create a sphere
    sphere = solver.create_sphere_body(
        body_id="sphere1",
        name="Bouncing Sphere",
        position=np.array([0.0, 5.0, 0.0]),
        radius=0.3,
        mass=1.0,
        restitution=0.8,
        friction=0.3
    )
    
    # Create a box
    box = solver.create_box_body(
        body_id="box1", 
        name="Tumbling Box",
        position=np.array([1.0, 4.0, 0.0]),
        dimensions=np.array([0.6, 0.4, 0.8]),
        mass=2.0,
        restitution=0.6,
        friction=0.5
    )
    
    # Give the box some initial angular velocity
    box.angular_velocity = np.array([2.0, 1.0, 0.5])
    
    # Create another sphere for collision testing
    sphere2 = solver.create_sphere_body(
        body_id="sphere2",
        name="Collision Sphere",
        position=np.array([0.8, 3.0, 0.0]),
        radius=0.25,
        mass=0.5,
        restitution=0.9,
        friction=0.2
    )
    
    print(f"Created {len(solver.bodies)} bodies:")
    for body in solver.bodies.values():
        print(f"  - {body.name}: {body.collision_geometry.geometry_type.value}")
        print(f"    Mass: {body.mass:.2f} kg")
        print(f"    Inertia diagonal: {np.diag(body.inertia_tensor.tensor)}")
    
    print("\nRunning simulation...")
    print("Time    Energy   Bodies  Contacts  Manifolds")
    print("-" * 45)
    
    # Run simulation
    for step in range(300):  # 5 seconds at 60 FPS
        solver.step(dt=1.0/60.0)
        
        # Print stats every 30 steps (0.5 seconds)
        if step % 30 == 0:
            stats = solver.get_stats()
            print(f"{stats['time']:5.2f}  {stats['total_energy']:7.2f}  "
                  f"{stats['num_bodies']:6d}  {stats['num_contacts']:8d}  "
                  f"{stats['num_manifolds']:9d}")
            
            # Show body positions occasionally
            if step % 60 == 0:
                print(f"  Sphere pos: [{sphere.position[0]:5.2f}, {sphere.position[1]:5.2f}, {sphere.position[2]:5.2f}]")
                print(f"  Box pos:    [{box.position[0]:5.2f}, {box.position[1]:5.2f}, {box.position[2]:5.2f}]")
                print(f"  Box angular vel: {np.linalg.norm(box.angular_velocity):.2f} rad/s")
    
    print("\nFinal Statistics:")
    final_stats = solver.get_stats()
    for key, value in final_stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")
    
    print("\nDemonstration complete!")
    print("\nKey Features Demonstrated:")
    print("✓ Proper inertia tensors for spheres and boxes")
    print("✓ Contact manifold generation and persistence")
    print("✓ Constraint-based collision resolution")
    print("✓ Symplectic semi-implicit integration")
    print("✓ Multiple geometry types")
    print("✓ Energy conservation monitoring")
    print("✓ Angular momentum and rotation dynamics")

def demo_energy_conservation():
    """Demonstrate energy conservation in the system."""
    print("\n" + "=" * 50)
    print("Energy Conservation Test")
    print("=" * 50)
    
    solver = RigidBodySolver(gravity=0.0)  # No gravity for conservation test
    
    # Create two spheres with initial velocities
    sphere1 = solver.create_sphere_body(
        body_id="s1",
        name="Sphere 1",
        position=np.array([-2.0, 0.0, 0.0]),
        radius=0.3,
        mass=1.0,
        restitution=1.0,  # Perfectly elastic
        friction=0.0
    )
    sphere1.velocity = np.array([2.0, 0.0, 0.0])
    
    sphere2 = solver.create_sphere_body(
        body_id="s2",
        name="Sphere 2", 
        position=np.array([2.0, 0.0, 0.0]),
        radius=0.3,
        mass=1.0,
        restitution=1.0,
        friction=0.0
    )
    sphere2.velocity = np.array([-2.0, 0.0, 0.0])
    
    initial_stats = solver.get_stats()
    initial_energy = initial_stats['total_energy']
    
    print(f"Initial energy: {initial_energy:.6f}")
    print("Running collision simulation...")
    
    max_energy_error = 0.0
    
    for step in range(120):  # 2 seconds
        solver.step(dt=1.0/60.0)
        
        stats = solver.get_stats()
        energy_error = abs(stats['total_energy'] - initial_energy) / initial_energy
        max_energy_error = max(max_energy_error, energy_error)
        
        if step % 20 == 0:
            print(f"Step {step:3d}: Energy = {stats['total_energy']:.6f}, "
                  f"Error = {energy_error*100:.4f}%")
    
    print(f"\nMaximum energy error: {max_energy_error*100:.4f}%")
    if max_energy_error < 0.01:  # Less than 1% error
        print("✓ Energy conservation: EXCELLENT")
    elif max_energy_error < 0.05:  # Less than 5% error
        print("✓ Energy conservation: GOOD")
    else:
        print("⚠ Energy conservation: NEEDS IMPROVEMENT")

if __name__ == "__main__":
    demo_enhanced_physics()
    demo_energy_conservation()
