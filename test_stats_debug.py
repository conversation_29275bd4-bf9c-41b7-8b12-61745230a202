#!/usr/bin/env python3
"""
Debug test for statistics calculation.
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sim.rigid3d import RigidBodySolver

def test_stats():
    """Test statistics calculation."""
    print("Testing statistics calculation...")
    
    solver = RigidBodySolver(gravity=9.81)
    
    # Create one sphere
    sphere = solver.create_sphere_body(
        body_id="sphere1",
        name="Sphere 1",
        position=np.array([0.0, 3.0, 0.0]),
        radius=0.3,
        mass=1.0
    )
    
    print(f"Created sphere")
    print(f"Inertia tensor: \n{sphere.inertia_tensor.tensor}")
    print(f"Angular velocity: {sphere.angular_velocity}")
    
    try:
        # Test world inertia tensor calculation
        I_world = sphere.get_world_inertia_tensor()
        print(f"World inertia tensor: \n{I_world}")
        
        # Test angular momentum calculation
        angular_momentum = I_world @ sphere.angular_velocity
        print(f"Angular momentum: {angular_momentum}")
        
        # Test stats
        stats = solver.get_stats()
        print(f"Stats: {stats}")
        
        return True
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_stats():
        print("Stats test passed!")
    else:
        print("Stats test failed!")
