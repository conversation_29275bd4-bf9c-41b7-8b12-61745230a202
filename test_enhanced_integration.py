#!/usr/bin/env python3
"""
Test enhanced rigid body integration with the main application.

Tests that the enhanced physics features are accessible through the NLP interface
and action executor.
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_enhanced_nlp_integration():
    """Test that enhanced physics commands work through NLP."""
    print("Testing Enhanced Physics NLP Integration")
    print("=" * 50)
    
    # Create scene and executor
    scene = Scene("Enhanced Physics Test")
    executor = ActionExecutor(scene)
    
    # Test commands for enhanced physics
    test_commands = [
        "Create a physics sphere at position 0,2,0 with radius 0.3 and mass 1.5",
        "Add a physics box at position 1,3,0 with dimensions 0.8,0.6,1.2 and mass 2.0",
        "Create a physics ball at position -1,4,0 with mass 0.8",
        "Run physics simulation"
    ]
    
    print("Testing NLP parsing...")
    for i, command in enumerate(test_commands):
        print(f"\nCommand {i+1}: '{command}'")
        
        # Parse command
        actions = parse_command(command)
        print(f"Parsed {len(actions)} action(s):")
        
        for action in actions:
            print(f"  - Type: {action.type}")
            print(f"  - Parameters: {action.parameters}")
            print(f"  - Confidence: {action.confidence}")
            
            # Execute action
            try:
                result = executor.execute(action)
                if result["success"]:
                    print(f"  ✓ Executed successfully: {result.get('message', 'OK')}")
                else:
                    print(f"  ✗ Execution failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"  ✗ Exception during execution: {e}")
    
    # Check final state
    print(f"\nFinal scene state:")
    print(f"  Objects in scene: {len(scene.objects)}")
    
    if executor.rigid_solver:
        print(f"  Rigid bodies: {len(executor.rigid_solver.bodies)}")
        stats = executor.rigid_solver.get_stats()
        print(f"  Total energy: {stats['total_energy']:.3f}")
        print(f"  Bodies: {stats['num_bodies']}")
        
        # Show body details
        for body in executor.rigid_solver.bodies.values():
            print(f"    - {body.name}: {body.collision_geometry.geometry_type.value}")
            print(f"      Mass: {body.mass:.2f} kg")
            print(f"      Position: [{body.position[0]:.2f}, {body.position[1]:.2f}, {body.position[2]:.2f}]")
            if hasattr(body.collision_geometry, 'radius'):
                print(f"      Radius: {body.collision_geometry.radius:.2f}")
            elif hasattr(body.collision_geometry, 'half_extents'):
                print(f"      Dimensions: {body.collision_geometry.half_extents * 2}")
    
    return True

def test_physics_simulation():
    """Test running a physics simulation with enhanced features."""
    print("\n" + "=" * 50)
    print("Testing Enhanced Physics Simulation")
    print("=" * 50)
    
    scene = Scene("Simulation Test")
    executor = ActionExecutor(scene)
    
    # Create objects using enhanced commands
    commands = [
        "Create a physics sphere at position 0,3,0 with radius 0.3 and mass 1.0",
        "Add a physics box at position 0.8,2.5,0 with dimensions 0.4,0.4,0.4 and mass 0.5"
    ]
    
    for command in commands:
        actions = parse_command(command)
        for action in actions:
            result = executor.execute(action)
            print(f"Created: {result.get('message', 'Object')}")
    
    # Run simulation steps
    print("\nRunning simulation...")
    print("Step  Time   Energy   Bodies  Contacts")
    print("-" * 40)
    
    for step in range(60):  # 1 second at 60 FPS
        result = executor.step_physics(dt=1.0/60.0)
        
        if result["success"] and step % 10 == 0:
            stats = result["stats"]
            print(f"{step:4d}  {stats['time']:5.2f}  {stats['total_energy']:7.2f}  "
                  f"{stats['num_bodies']:6d}  {stats['num_contacts']:8d}")
    
    print("\nSimulation completed successfully!")
    return True

def test_backward_compatibility():
    """Test that old commands still work."""
    print("\n" + "=" * 50)
    print("Testing Backward Compatibility")
    print("=" * 50)
    
    scene = Scene("Compatibility Test")
    executor = ActionExecutor(scene)
    
    # Test old-style commands
    old_commands = [
        "Create a ball 2m high",
        "Add a sphere at position 1,1,0 with radius 0.4",
        "Run physics"
    ]
    
    for command in old_commands:
        print(f"\nTesting: '{command}'")
        actions = parse_command(command)
        
        for action in actions:
            result = executor.execute(action)
            if result["success"]:
                print(f"  ✓ {result.get('message', 'Success')}")
            else:
                print(f"  ✗ {result.get('error', 'Failed')}")
    
    print("\nBackward compatibility test completed!")
    return True

def main():
    """Run all integration tests."""
    print("Enhanced Rigid Body Physics Integration Tests")
    print("=" * 60)
    
    tests = [
        test_enhanced_nlp_integration,
        test_physics_simulation,
        test_backward_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"Integration tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("Enhanced physics features are now available in the main application!")
        return 0
    else:
        print("❌ Some integration tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
