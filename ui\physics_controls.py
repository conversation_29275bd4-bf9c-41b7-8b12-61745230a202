"""
Physics Controls Panel for NeoPhysics.

Provides controls for physics simulation parameters and real-time execution.
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QSlider, QLabel, QPushButton, QDoubleSpinBox,
                            QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

class PhysicsControls(QWidget):
    """Physics simulation controls widget."""
    
    # Signals
    gravity_changed = pyqtSignal(float)
    simulation_started = pyqtSignal()
    simulation_stopped = pyqtSignal()
    simulation_reset = pyqtSignal()
    timestep_changed = pyqtSignal(float)
    
    def __init__(self):
        super().__init__()
        self.is_running = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulation_step)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the physics controls UI."""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Physics Controls")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Simulation controls
        sim_group = QGroupBox("Simulation")
        sim_layout = QVBoxLayout(sim_group)
        
        # Play/Pause/Reset buttons
        button_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶ Play")
        self.play_btn.clicked.connect(self.toggle_simulation)
        button_layout.addWidget(self.play_btn)
        
        self.reset_btn = QPushButton("⏹ Reset")
        self.reset_btn.clicked.connect(self.reset_simulation)
        button_layout.addWidget(self.reset_btn)
        
        sim_layout.addLayout(button_layout)
        
        # Timestep control
        timestep_layout = QHBoxLayout()
        timestep_layout.addWidget(QLabel("Timestep:"))
        
        self.timestep_spin = QDoubleSpinBox()
        self.timestep_spin.setRange(0.001, 0.1)
        self.timestep_spin.setSingleStep(0.001)
        self.timestep_spin.setDecimals(3)
        self.timestep_spin.setValue(1.0/60.0)  # 60 FPS
        self.timestep_spin.setSuffix(" s")
        self.timestep_spin.valueChanged.connect(self.on_timestep_changed)
        timestep_layout.addWidget(self.timestep_spin)
        
        sim_layout.addLayout(timestep_layout)
        layout.addWidget(sim_group)
        
        # Gravity controls
        gravity_group = QGroupBox("Gravity")
        gravity_layout = QVBoxLayout(gravity_group)
        
        # Gravity slider
        gravity_layout.addWidget(QLabel("Gravity (m/s²):"))
        
        self.gravity_slider = QSlider(Qt.Orientation.Horizontal)
        self.gravity_slider.setMinimum(0)
        self.gravity_slider.setMaximum(200)  # 0 to 20 m/s²
        self.gravity_slider.setValue(98)  # 9.8 m/s²
        self.gravity_slider.valueChanged.connect(self.on_gravity_slider_changed)
        gravity_layout.addWidget(self.gravity_slider)
        
        # Gravity value display
        gravity_value_layout = QHBoxLayout()
        self.gravity_value = QLabel("9.8")
        self.gravity_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        gravity_value_layout.addWidget(self.gravity_value)
        
        # Gravity presets
        preset_layout = QHBoxLayout()
        
        earth_btn = QPushButton("Earth")
        earth_btn.clicked.connect(lambda: self.set_gravity(9.81))
        preset_layout.addWidget(earth_btn)
        
        moon_btn = QPushButton("Moon")
        moon_btn.clicked.connect(lambda: self.set_gravity(1.62))
        preset_layout.addWidget(moon_btn)
        
        mars_btn = QPushButton("Mars")
        mars_btn.clicked.connect(lambda: self.set_gravity(3.71))
        preset_layout.addWidget(mars_btn)
        
        zero_btn = QPushButton("Zero G")
        zero_btn.clicked.connect(lambda: self.set_gravity(0.0))
        preset_layout.addWidget(zero_btn)
        
        gravity_layout.addLayout(gravity_value_layout)
        gravity_layout.addLayout(preset_layout)
        layout.addWidget(gravity_group)
        
        # Ground controls
        ground_group = QGroupBox("Ground")
        ground_layout = QVBoxLayout(ground_group)
        
        self.ground_enabled = QCheckBox("Enable Ground Plane")
        self.ground_enabled.setChecked(True)
        ground_layout.addWidget(self.ground_enabled)
        
        # Ground level
        ground_level_layout = QHBoxLayout()
        ground_level_layout.addWidget(QLabel("Ground Y:"))
        
        self.ground_level = QDoubleSpinBox()
        self.ground_level.setRange(-10.0, 10.0)
        self.ground_level.setSingleStep(0.1)
        self.ground_level.setValue(0.0)
        self.ground_level.setSuffix(" m")
        ground_level_layout.addWidget(self.ground_level)
        
        ground_layout.addLayout(ground_level_layout)
        layout.addWidget(ground_group)
        
        # Statistics
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.time_label = QLabel("Time: 0.00 s")
        stats_layout.addWidget(self.time_label)
        
        self.energy_label = QLabel("Energy: 0.00 J")
        stats_layout.addWidget(self.energy_label)
        
        self.bodies_label = QLabel("Bodies: 0")
        stats_layout.addWidget(self.bodies_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
    
    def toggle_simulation(self):
        """Toggle simulation play/pause."""
        if self.is_running:
            self.stop_simulation()
        else:
            self.start_simulation()
    
    def start_simulation(self):
        """Start the simulation."""
        self.is_running = True
        self.play_btn.setText("⏸ Pause")
        
        # Start timer at 60 FPS
        fps = int(1.0 / self.timestep_spin.value())
        self.timer.start(1000 // fps)
        
        self.simulation_started.emit()
    
    def stop_simulation(self):
        """Stop the simulation."""
        self.is_running = False
        self.play_btn.setText("▶ Play")
        self.timer.stop()
        self.simulation_stopped.emit()
    
    def reset_simulation(self):
        """Reset the simulation."""
        was_running = self.is_running
        if self.is_running:
            self.stop_simulation()
        
        self.simulation_reset.emit()
        
        # Reset displays
        self.time_label.setText("Time: 0.00 s")
        self.energy_label.setText("Energy: 0.00 J")
        
        if was_running:
            self.start_simulation()
    
    def simulation_step(self):
        """Called on each simulation step."""
        # This will be connected to the actual physics step
        pass
    
    def on_gravity_slider_changed(self, value: int):
        """Handle gravity slider change."""
        gravity = value / 10.0  # Convert to m/s²
        self.gravity_value.setText(f"{gravity:.1f}")
        self.gravity_changed.emit(gravity)
    
    def set_gravity(self, gravity: float):
        """Set gravity value."""
        slider_value = int(gravity * 10)
        self.gravity_slider.setValue(slider_value)
        self.gravity_value.setText(f"{gravity:.2f}")
        self.gravity_changed.emit(gravity)
    
    def on_timestep_changed(self, timestep: float):
        """Handle timestep change."""
        self.timestep_changed.emit(timestep)
        
        # Update timer if running
        if self.is_running:
            fps = int(1.0 / timestep)
            self.timer.start(1000 // fps)
    
    def update_stats(self, stats: dict):
        """Update statistics display."""
        self.time_label.setText(f"Time: {stats.get('time', 0):.2f} s")
        self.energy_label.setText(f"Energy: {stats.get('total_energy', 0):.2f} J")
        self.bodies_label.setText(f"Bodies: {stats.get('num_bodies', 0)}")
    
    def get_gravity(self) -> float:
        """Get current gravity value."""
        return self.gravity_slider.value() / 10.0
    
    def get_timestep(self) -> float:
        """Get current timestep."""
        return self.timestep_spin.value()
    
    def is_ground_enabled(self) -> bool:
        """Check if ground is enabled."""
        return self.ground_enabled.isChecked()
    
    def get_ground_level(self) -> float:
        """Get ground level."""
        return self.ground_level.value()