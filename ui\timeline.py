"""
Timeline for NeoPhysics.

Provides simulation playback controls and time scrubbing.
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QPushButton, QSlider, 
                            QLabel, QSpinBox, QDoubleSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon

class Timeline(QWidget):
    """Timeline widget for simulation control."""
    
    play_requested = pyqtSignal()
    pause_requested = pyqtSignal()
    step_requested = pyqtSignal()
    reset_requested = pyqtSignal()
    time_changed = pyqtSignal(float)  # time
    
    def __init__(self):
        super().__init__()
        self.is_playing = False
        self.current_time = 0.0
        self.max_time = 10.0
        self.time_step = 0.01
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the timeline UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Play controls
        self.play_btn = QPushButton("▶")
        self.play_btn.setMaximumWidth(40)
        self.play_btn.clicked.connect(self.toggle_play)
        layout.addWidget(self.play_btn)
        
        self.step_btn = QPushButton("⏭")
        self.step_btn.setMaximumWidth(40)
        self.step_btn.clicked.connect(self.step_simulation)
        layout.addWidget(self.step_btn)
        
        self.reset_btn = QPushButton("⏹")
        self.reset_btn.setMaximumWidth(40)
        self.reset_btn.clicked.connect(self.reset_simulation)
        layout.addWidget(self.reset_btn)
        
        # Time display
        self.time_label = QLabel("Time:")
        layout.addWidget(self.time_label)
        
        self.time_display = QLabel("0.00s")
        self.time_display.setMinimumWidth(60)
        layout.addWidget(self.time_display)
        
        # Time slider
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setMinimum(0)
        self.time_slider.setMaximum(1000)
        self.time_slider.setValue(0)
        self.time_slider.valueChanged.connect(self.on_slider_changed)
        layout.addWidget(self.time_slider)
        
        # Time step control
        layout.addWidget(QLabel("dt:"))
        
        self.dt_spin = QDoubleSpinBox()
        self.dt_spin.setRange(0.001, 1.0)
        self.dt_spin.setSingleStep(0.001)
        self.dt_spin.setDecimals(3)
        self.dt_spin.setValue(self.time_step)
        self.dt_spin.setSuffix("s")
        self.dt_spin.setMaximumWidth(80)
        self.dt_spin.valueChanged.connect(self.set_time_step)
        layout.addWidget(self.dt_spin)
        
        # Max time control
        layout.addWidget(QLabel("Max:"))
        
        self.max_time_spin = QDoubleSpinBox()
        self.max_time_spin.setRange(1.0, 1000.0)
        self.max_time_spin.setSingleStep(1.0)
        self.max_time_spin.setValue(self.max_time)
        self.max_time_spin.setSuffix("s")
        self.max_time_spin.setMaximumWidth(80)
        self.max_time_spin.valueChanged.connect(self.set_max_time)
        layout.addWidget(self.max_time_spin)
    
    def toggle_play(self):
        """Toggle play/pause."""
        if self.is_playing:
            self.pause_simulation()
        else:
            self.play_simulation()
    
    def play_simulation(self):
        """Start playing simulation."""
        self.is_playing = True
        self.play_btn.setText("⏸")
        self.play_requested.emit()
    
    def pause_simulation(self):
        """Pause simulation."""
        self.is_playing = False
        self.play_btn.setText("▶")
        self.pause_requested.emit()
    
    def step_simulation(self):
        """Step simulation by one frame."""
        self.step_requested.emit()
    
    def reset_simulation(self):
        """Reset simulation to beginning."""
        self.is_playing = False
        self.play_btn.setText("▶")
        self.current_time = 0.0
        self.update_display()
        self.reset_requested.emit()
    
    def set_time(self, time: float):
        """Set current time."""
        self.current_time = max(0.0, min(time, self.max_time))
        self.update_display()
    
    def set_time_step(self, dt: float):
        """Set time step."""
        self.time_step = dt
    
    def set_max_time(self, max_time: float):
        """Set maximum time."""
        self.max_time = max_time
        if self.current_time > max_time:
            self.current_time = max_time
        self.update_display()
    
    def update_display(self):
        """Update time display and slider."""
        # Update time label
        self.time_display.setText(f"{self.current_time:.2f}s")
        
        # Update slider position
        if self.max_time > 0:
            slider_pos = int(1000 * self.current_time / self.max_time)
            self.time_slider.blockSignals(True)
            self.time_slider.setValue(slider_pos)
            self.time_slider.blockSignals(False)
    
    def on_slider_changed(self, value: int):
        """Handle slider position change."""
        time = (value / 1000.0) * self.max_time
        self.set_time(time)
        self.time_changed.emit(self.current_time)
    
    def advance_time(self):
        """Advance time by one step."""
        self.set_time(self.current_time + self.time_step)
        self.time_changed.emit(self.current_time)