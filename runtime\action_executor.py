"""
Action Executor for NeoPhysics.

Executes parsed natural language actions on the scene and physics solvers.
"""

import numpy as np
from typing import Dict, Any, Optional
from core.scene import Scene, SceneObject, ObjectType, Transform
from sim.rigid3d import RigidBodySolver, RigidBody, BodyType
from nlp.parser import Action

class ActionExecutor:
    """Executes actions on the physics simulation."""
    
    def __init__(self, scene: Scene):
        self.scene = scene
        self.rigid_solver: Optional[RigidBodySolver] = None
        self.simulation_running = False
    
    def execute(self, action: Action) -> Dict[str, Any]:
        """Execute an action and return result."""
        action_type = action.type
        params = action.parameters
        
        try:
            if action_type == "add_cube":
                return self._add_cube(params)
            elif action_type == "add_ball":
                return self._add_ball(params)
            elif action_type == "add_sphere":
                return self._add_sphere(params)
            elif action_type == "add_cube":
                return self._add_cube(params)
            elif action_type == "add_plane":
                return self._add_plane(params)
            elif action_type == "set_position":
                return self._set_position(params)
            elif action_type == "set_velocity":
                return self._set_velocity(params)
            elif action_type == "set_mass":
                return self._set_mass(params)
            elif action_type == "run_physics":
                return self._run_physics(params)
            elif action_type == "clear_scene":
                return self._clear_scene(params)
            elif action_type == "query_scene":
                return self._query_scene(params)
            else:
                return {"success": False, "error": f"Unknown action: {action_type}"}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _add_cube(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a cube to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        size = params.get("size", 1.0)
        
        obj_id = self.scene.create_cube(
            name=name,
            position=tuple(position),
            size=size,
            material="default_thermal",
            temperature=20.0
        )
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created cube '{obj.name}' at {position}"
        }
    
    def _add_sphere(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a sphere to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        radius = params.get("radius", 0.5)
        
        obj_id = self.scene.create_sphere(
            name=name,
            position=tuple(position),
            radius=radius,
            material="default_thermal",
            temperature=20.0
        )
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created sphere '{obj.name}' at {position}"
        }
    
    def _add_plane(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a plane to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        size = params.get("size", 2.0)
        
        transform = Transform(position=np.array(position), scale=np.array([size, size, 0.1]))
        
        obj = SceneObject(
            name=name or "plane",
            object_type=ObjectType.PLANE,
            transform=transform,
            material=self.scene.materials.get("default_thermal"),
            properties={"temperature": params.get("temperature", 20.0)}
        )
        
        obj_id = self.scene.add_object(obj)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created plane '{obj.name}' at {position}"
        }
    

    
    def _set_position(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object position."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        
        obj = self.scene.get_object_by_name(name)
        if not obj:
            return {"success": False, "error": f"Object '{name}' not found"}
        
        obj.transform.position = np.array(position)
        return {
            "success": True,
            "message": f"Moved '{name}' to {position}"
        }
    

    
    def _clear_scene(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Clear the scene."""
        self.scene.clear()
        self.rigid_solver = None
        return {"success": True, "message": "Scene cleared"}
    
    def _query_scene(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Query scene information."""
        summary = self.scene.get_summary()
        return {"success": True, "data": summary, "message": "Scene summary retrieved"}
    
    def _add_ball(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a physics ball to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 2, 0])  # Default 2m high
        radius = params.get("radius", 0.1)  # 10cm radius
        mass = params.get("mass", 1.0)
        velocity = params.get("velocity", [0, 0, 0])
        
        # Create scene object
        obj_id = self.scene.create_sphere(
            name=name or "ball",
            position=tuple(position),
            radius=radius,
            material="default_thermal",
            temperature=20.0
        )
        
        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()
        
        # Create rigid body
        rigid_body = RigidBody(
            id=obj_id,
            name=name or "ball",
            body_type=BodyType.DYNAMIC,
            position=np.array(position, dtype=float),
            mass=mass,
            radius=radius,
            velocity=np.array(velocity, dtype=float)
        )
        
        self.rigid_solver.add_body(rigid_body)
        
        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created physics ball '{obj.name}' at {position} with mass {mass}kg"
        }
    
    def _run_physics(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Run rigid body physics simulation."""
        if self.rigid_solver is None:
            return {"success": False, "error": "No rigid bodies in scene"}
        
        return {
            "success": True,
            "message": "Started rigid body physics simulation"
        }
    
    def _set_velocity(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object velocity."""
        name = params.get("name", "")
        velocity = params.get("velocity", [0, 0, 0])
        
        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}
        
        # Find rigid body by name
        for body in self.rigid_solver.bodies.values():
            if body.name == name:
                body.velocity = np.array(velocity, dtype=float)
                return {
                    "success": True,
                    "message": f"Set velocity of '{name}' to {velocity} m/s"
                }
        
        return {"success": False, "error": f"Physics body '{name}' not found"}
    
    def _set_mass(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object mass."""
        name = params.get("name", "")
        mass = params.get("mass", 1.0)
        
        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}
        
        # Find rigid body by name
        for body in self.rigid_solver.bodies.values():
            if body.name == name:
                body.mass = mass
                return {
                    "success": True,
                    "message": f"Set mass of '{name}' to {mass} kg"
                }
        
        return {"success": False, "error": f"Physics body '{name}' not found"}
    

    
    def step_physics(self, dt: float = None) -> Dict[str, Any]:
        """Step the physics simulation."""
        if self.rigid_solver is not None:
            self.rigid_solver.step(dt)
            
            # Update scene object positions
            for body in self.rigid_solver.bodies.values():
                obj = self.scene.get_object(body.id)
                if obj:
                    obj.transform.position = body.position.copy()
            
            return {"success": True, "stats": self.rigid_solver.get_stats()}
        
        return {"success": False, "error": "No active physics simulation"}
    
    def get_rigid_solver(self) -> Optional[RigidBodySolver]:
        """Get the rigid body solver."""
        return self.rigid_solver
    
    def set_gravity(self, gravity: float):
        """Set gravity for rigid body simulation."""
        if self.rigid_solver is not None:
            self.rigid_solver.set_gravity(gravity)