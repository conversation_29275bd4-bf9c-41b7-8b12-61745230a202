"""
Command Panel for NeoPhysics.

Provides natural language input interface and command history.
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
                            QPushButton, QLabel, QListWidget, QSplitter,
                            QGroupBox, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor

class CommandPanel(QWidget):
    """Command input panel with natural language interface."""
    
    command_submitted = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.command_history = []
        self.history_index = -1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the command panel UI."""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Natural Language Commands")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(title_label)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # Command input section
        input_group = QGroupBox("Command Input")
        input_layout = QVBoxLayout(input_group)
        
        # Command input text area
        self.command_input = QTextEdit()
        self.command_input.setMaximumHeight(100)
        self.command_input.setPlaceholderText(
            "Enter natural language commands here...\n"
            "Examples:\n"
            "• Place a hot cube at center\n"
            "• Add copper sphere at 2,1,0\n"
            "• Run simulation for 100 steps"
        )
        input_layout.addWidget(self.command_input)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.submit_btn = QPushButton("Execute (Ctrl+Enter)")
        self.submit_btn.clicked.connect(self.submit_command)
        button_layout.addWidget(self.submit_btn)
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_input)
        button_layout.addWidget(self.clear_btn)
        
        input_layout.addLayout(button_layout)
        splitter.addWidget(input_group)
        
        # Command history section
        history_group = QGroupBox("Command History")
        history_layout = QVBoxLayout(history_group)
        
        self.history_list = QListWidget()
        self.history_list.itemDoubleClicked.connect(self.reuse_command)
        history_layout.addWidget(self.history_list)
        
        # History controls
        history_controls = QHBoxLayout()
        
        clear_history_btn = QPushButton("Clear History")
        clear_history_btn.clicked.connect(self.clear_history)
        history_controls.addWidget(clear_history_btn)
        
        history_controls.addStretch()
        history_layout.addLayout(history_controls)
        
        splitter.addWidget(history_group)
        
        # Examples section
        examples_group = QGroupBox("Example Commands")
        examples_layout = QVBoxLayout(examples_group)
        
        examples_scroll = QScrollArea()
        examples_widget = QWidget()
        examples_content_layout = QVBoxLayout(examples_widget)
        
        # Add example commands
        examples = [
            ("Physics Objects", [
                "Create a ball 2m high",
                "Create a ball at position 0,5,0 with mass 2kg",
                "Add a sphere with velocity 5,0,0",
                "Place a physics ball with radius 0.2"
            ]),
            ("Basic Objects", [
                "Place a cube at the center",
                "Add a hot sphere at position 2,0,0",
                "Create a copper cube with size 0.5",
                "Place an insulator sphere with radius 0.3"
            ]),
            ("Physics Simulation", [
                "Run physics",
                "Set gravity to 3.71",
                "Set velocity of ball to 2,0,0",
                "Set mass of ball to 5kg"
            ]),
            ("Heat Simulation", [
                "Run simulation for 100 steps",
                "Simulate for 5 seconds",
                "Create temperature field",
                "Run heat diffusion"
            ]),
            ("Scene Management", [
                "Clear the scene",
                "Show scene summary",
                "List all objects"
            ])
        ]
        
        for category, commands in examples:
            category_label = QLabel(category)
            category_label.setStyleSheet("font-weight: bold; color: #333; margin-top: 10px;")
            examples_content_layout.addWidget(category_label)
            
            for cmd in commands:
                cmd_btn = QPushButton(cmd)
                cmd_btn.setStyleSheet("text-align: left; padding: 5px; margin: 2px;")
                cmd_btn.clicked.connect(lambda checked, c=cmd: self.insert_example(c))
                examples_content_layout.addWidget(cmd_btn)
        
        examples_content_layout.addStretch()
        examples_scroll.setWidget(examples_widget)
        examples_layout.addWidget(examples_scroll)
        
        splitter.addWidget(examples_group)
        
        # Set splitter proportions
        splitter.setSizes([150, 200, 300])
        
        # Connect keyboard shortcuts
        self.command_input.keyPressEvent = self.handle_key_press
    
    def handle_key_press(self, event):
        """Handle key press events in command input."""
        if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.submit_command()
        elif event.key() == Qt.Key.Key_Up and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.previous_command()
        elif event.key() == Qt.Key.Key_Down and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.next_command()
        else:
            # Call the original keyPressEvent
            QTextEdit.keyPressEvent(self.command_input, event)
    
    def submit_command(self):
        """Submit the current command."""
        command = self.command_input.toPlainText().strip()
        if not command:
            return
        
        # Add to history
        self.add_to_history(command)
        
        # Emit signal
        self.command_submitted.emit(command)
        
        # Clear input
        self.command_input.clear()
        
        # Reset history index
        self.history_index = -1
    
    def clear_input(self):
        """Clear the command input."""
        self.command_input.clear()
    
    def add_to_history(self, command: str):
        """Add command to history."""
        # Avoid duplicates
        if command in self.command_history:
            self.command_history.remove(command)
        
        # Add to beginning of history
        self.command_history.insert(0, command)
        
        # Limit history size
        if len(self.command_history) > 50:
            self.command_history = self.command_history[:50]
        
        # Update UI
        self.update_history_list()
    
    def update_history_list(self):
        """Update the history list widget."""
        self.history_list.clear()
        for cmd in self.command_history:
            self.history_list.addItem(cmd)
    
    def clear_history(self):
        """Clear command history."""
        self.command_history.clear()
        self.history_list.clear()
        self.history_index = -1
    
    def reuse_command(self, item):
        """Reuse a command from history."""
        command = item.text()
        self.command_input.setPlainText(command)
        self.command_input.setFocus()
    
    def previous_command(self):
        """Navigate to previous command in history."""
        if not self.command_history:
            return
        
        self.history_index = min(self.history_index + 1, len(self.command_history) - 1)
        command = self.command_history[self.history_index]
        self.command_input.setPlainText(command)
        
        # Move cursor to end
        cursor = self.command_input.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.command_input.setTextCursor(cursor)
    
    def next_command(self):
        """Navigate to next command in history."""
        if not self.command_history or self.history_index <= 0:
            self.history_index = -1
            self.command_input.clear()
            return
        
        self.history_index -= 1
        command = self.command_history[self.history_index]
        self.command_input.setPlainText(command)
        
        # Move cursor to end
        cursor = self.command_input.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.command_input.setTextCursor(cursor)
    
    def insert_example(self, command: str):
        """Insert an example command into the input."""
        self.command_input.setPlainText(command)
        self.command_input.setFocus()
        
        # Move cursor to end
        cursor = self.command_input.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.command_input.setTextCursor(cursor)