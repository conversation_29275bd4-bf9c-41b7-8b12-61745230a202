#!/usr/bin/env python3
"""
Test rigid body training pipeline.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from pathlib import Path
from data.generator import PhysicsDataGenerator

def main():
    """Test rigid body training pipeline."""
    print("RIGID BODY TRAINING PIPELINE TEST")
    print("=" * 50)
    
    # 1. Generate rigid body training data
    print("\n1. Generating rigid body training data...")
    output_dir = Path("rigid_training_data")
    output_dir.mkdir(exist_ok=True)
    
    generator = PhysicsDataGenerator(output_dir)
    
    # Generate data with multiple scenarios
    rigid_files = generator.generate_rigid_dataset(
        num_scenarios=10,  # More scenarios for better training
        timesteps=200      # Longer sequences
    )
    
    print(f"Generated {len(rigid_files)} rigid body datasets")
    
    # 2. Show data structure
    print("\n2. Data structure:")
    if rigid_files:
        import h5py
        import json
        
        with h5py.File(rigid_files[0], 'r') as f:
            metadata = json.loads(f.attrs['metadata'])
            print(f"Dataset: {metadata['dataset_id']}")
            print(f"Physics type: {metadata['physics_type']}")
            print(f"Timesteps: {metadata['num_timesteps']}")
            print(f"Gravity: {metadata['solver_params']['gravity']}")
            
            positions = f['positions']
            velocities = f['velocities']
            print(f"Positions shape: {positions.shape}")  # [timesteps, num_bodies, 3]
            print(f"Velocities shape: {velocities.shape}")  # [timesteps, num_bodies, 3]
    
    # 3. Train the model
    print(f"\n3. Training rigid body neural network...")
    print("Command to run:")
    print(f"python train/train_rigid3d.py --data_dir {output_dir} --batch_size 4 --epochs 20 --lr 1e-3")
    
    print(f"\n4. Usage after training:")
    print("# Load trained model")
    print("from train.train_rigid3d import RigidBodyNet")
    print("import torch")
    print("")
    print("model = RigidBodyNet()")
    print("checkpoint = torch.load('checkpoints/best_rigid_model.pth')")
    print("model.load_state_dict(checkpoint['model_state_dict'])")
    print("model.eval()")
    print("")
    print("# Use for fast physics prediction")
    print("# input: [batch, num_bodies, 7] -> output: [batch, num_bodies, 6]")
    
    print(f"\n[SUCCESS] Rigid body training pipeline ready!")
    print("Run the training command above to train the neural network.")

if __name__ == "__main__":
    main()