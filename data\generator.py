"""
Physics Data Generator for NeoPhysics.

Generates training data for neural surrogate models using deterministic solvers.
"""

import numpy as np
import h5py
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
import uuid
from datetime import datetime

from sim.heat3d import Heat3DSolver
from sim.fluids3d import FluidSolver3D
from sim.rigid3d import RigidBodySolver, RigidBody, BodyType

@dataclass
class DatasetMetadata:
    """Metadata for physics dataset."""
    dataset_id: str
    physics_type: str  # "heat", "fluid", "rigid"
    grid_size: Tuple[int, int, int]
    grid_spacing: float
    num_timesteps: int
    dt: float
    total_time: float
    solver_params: Dict[str, Any]
    boundary_conditions: Dict[str, Any]
    created_at: str
    commit_id: str = "dev"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class PhysicsDataGenerator:
    """Generates physics simulation data for ML training."""
    
    def __init__(self, output_dir: Path):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.shard_size = 100  # Timesteps per shard
    
    def generate_heat_dataset(self, 
                            grid_sizes: List[Tuple[int, int, int]] = [(32, 32, 32), (64, 64, 64)],
                            num_scenarios: int = 10,
                            timesteps: int = 200) -> List[str]:
        """Generate heat diffusion training data."""
        dataset_files = []
        
        for grid_size in grid_sizes:
            for scenario in range(num_scenarios):
                dataset_id = f"heat_{grid_size[0]}x{grid_size[1]}x{grid_size[2]}_scenario_{scenario:03d}"
                
                # Create solver
                solver = Heat3DSolver(
                    grid_size=grid_size,
                    grid_spacing=0.1,
                    thermal_diffusivity=np.random.uniform(0.5, 2.0)
                )
                
                # Random initial conditions
                initial_temp = np.random.uniform(0, 100, grid_size)
                solver.set_initial_temperature(initial_temp)
                
                # Random boundary conditions
                self._add_random_heat_boundaries(solver, grid_size)
                
                # Generate data
                metadata = DatasetMetadata(
                    dataset_id=dataset_id,
                    physics_type="heat",
                    grid_size=grid_size,
                    grid_spacing=0.1,
                    num_timesteps=timesteps,
                    dt=solver.dt,
                    total_time=timesteps * solver.dt,
                    solver_params={"thermal_diffusivity": solver.alpha},
                    boundary_conditions={},
                    created_at=datetime.now().isoformat()
                )
                
                file_path = self._generate_time_series(solver, metadata, timesteps)
                dataset_files.append(file_path)
        
        return dataset_files
    
    def generate_fluid_dataset(self,
                             grid_sizes: List[Tuple[int, int, int]] = [(32, 32, 32)],
                             num_scenarios: int = 5,
                             timesteps: int = 100) -> List[str]:
        """Generate fluid dynamics training data."""
        dataset_files = []
        
        for grid_size in grid_sizes:
            for scenario in range(num_scenarios):
                dataset_id = f"fluid_{grid_size[0]}x{grid_size[1]}x{grid_size[2]}_scenario_{scenario:03d}"
                
                # Create solver
                solver = FluidSolver3D(
                    grid_size=grid_size,
                    grid_spacing=0.1,
                    viscosity=np.random.uniform(0.001, 0.1),
                    density=1.0
                )
                
                # Random inflow conditions
                self._add_random_fluid_conditions(solver, grid_size)
                
                # Generate data
                metadata = DatasetMetadata(
                    dataset_id=dataset_id,
                    physics_type="fluid",
                    grid_size=grid_size,
                    grid_spacing=0.1,
                    num_timesteps=timesteps,
                    dt=solver.dt,
                    total_time=timesteps * solver.dt,
                    solver_params={"viscosity": solver.viscosity, "density": solver.density},
                    boundary_conditions={},
                    created_at=datetime.now().isoformat()
                )
                
                file_path = self._generate_fluid_time_series(solver, metadata, timesteps)
                dataset_files.append(file_path)
        
        return dataset_files
    
    def generate_rigid_dataset(self,
                             num_scenarios: int = 20,
                             timesteps: int = 300) -> List[str]:
        """Generate rigid body dynamics training data."""
        dataset_files = []
        
        for scenario in range(num_scenarios):
            dataset_id = f"rigid_scenario_{scenario:03d}"
            
            # Create solver
            solver = RigidBodySolver(gravity=np.random.uniform(5.0, 15.0))
            
            # Random rigid body setup
            self._add_random_rigid_bodies(solver)
            
            # Generate data
            metadata = DatasetMetadata(
                dataset_id=dataset_id,
                physics_type="rigid",
                grid_size=(1, 1, 1),  # Not applicable for rigid bodies
                grid_spacing=1.0,
                num_timesteps=timesteps,
                dt=solver.dt,
                total_time=timesteps * solver.dt,
                solver_params={"gravity": solver.gravity},
                boundary_conditions={"ground_y": solver.ground_y},
                created_at=datetime.now().isoformat()
            )
            
            file_path = self._generate_rigid_time_series(solver, metadata, timesteps)
            dataset_files.append(file_path)
        
        return dataset_files
    
    def _add_random_heat_boundaries(self, solver: Heat3DSolver, grid_size: Tuple[int, int, int]):
        """Add random boundary conditions for heat solver."""
        nx, ny, nz = grid_size
        
        # Random hot/cold objects
        num_objects = np.random.randint(1, 4)
        for _ in range(num_objects):
            # Random position
            x = np.random.uniform(0, nx * solver.dx)
            y = np.random.uniform(0, ny * solver.dx)
            z = np.random.uniform(0, nz * solver.dx)
            
            # Random temperature and size
            temp = np.random.uniform(-50, 150)
            size = np.random.uniform(0.5, 2.0)
            
            # Random object type
            obj_type = np.random.choice(["cube", "sphere"])
            solver.add_object_boundary((x, y, z), size, temp, obj_type)
    
    def _add_random_fluid_conditions(self, solver: FluidSolver3D, grid_size: Tuple[int, int, int]):
        """Add random boundary conditions for fluid solver."""
        nx, ny, nz = grid_size
        
        # Random inflow
        inflow_x = np.random.randint(0, nx)
        inflow_y = np.random.randint(0, ny)
        inflow_z = np.random.randint(0, nz)
        
        vel_x = np.random.uniform(-2, 2)
        vel_y = np.random.uniform(-2, 2)
        vel_z = np.random.uniform(-2, 2)
        
        solver.add_inflow((inflow_x, inflow_y, inflow_z), (vel_x, vel_y, vel_z))
        
        # Random obstacles
        num_obstacles = np.random.randint(0, 3)
        for _ in range(num_obstacles):
            obs_x = np.random.randint(5, nx-5)
            obs_y = np.random.randint(5, ny-5)
            obs_z = np.random.randint(5, nz-5)
            size = np.random.randint(1, 3)
            solver.add_obstacle((obs_x, obs_y, obs_z), size)
    
    def _add_random_rigid_bodies(self, solver: RigidBodySolver):
        """Add random rigid bodies to solver."""
        num_bodies = np.random.randint(1, 5)
        
        for i in range(num_bodies):
            body = RigidBody(
                id=f"body_{i}",
                name=f"body_{i}",
                body_type=BodyType.DYNAMIC,
                position=np.array([
                    np.random.uniform(-2, 2),
                    np.random.uniform(1, 5),
                    np.random.uniform(-2, 2)
                ]),
                mass=np.random.uniform(0.5, 3.0),
                radius=np.random.uniform(0.1, 0.5),
                velocity=np.array([
                    np.random.uniform(-1, 1),
                    np.random.uniform(-1, 1),
                    np.random.uniform(-1, 1)
                ])
            )
            solver.add_body(body)
    
    def _generate_time_series(self, solver: Heat3DSolver, metadata: DatasetMetadata, 
                            timesteps: int) -> str:
        """Generate heat diffusion time series data."""
        file_path = self.output_dir / f"{metadata.dataset_id}.h5"
        
        with h5py.File(file_path, 'w') as f:
            # Store metadata
            f.attrs['metadata'] = json.dumps(metadata.to_dict())
            
            # Create datasets
            temp_data = f.create_dataset('temperature', 
                                       shape=(timesteps + 1, *metadata.grid_size),
                                       dtype=np.float32)
            
            # Store initial state
            temp_data[0] = solver.get_temperature_field()
            
            # Run simulation
            for t in range(timesteps):
                solver.step()
                temp_data[t + 1] = solver.get_temperature_field()
                
                if (t + 1) % 50 == 0:
                    print(f"  Generated {t + 1}/{timesteps} timesteps for {metadata.dataset_id}")
        
        # Save metadata separately
        metadata_path = self.output_dir / f"{metadata.dataset_id}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata.to_dict(), f, indent=2)
        
        return str(file_path)
    
    def _generate_fluid_time_series(self, solver: FluidSolver3D, metadata: DatasetMetadata,
                                  timesteps: int) -> str:
        """Generate fluid dynamics time series data."""
        file_path = self.output_dir / f"{metadata.dataset_id}.h5"
        
        with h5py.File(file_path, 'w') as f:
            # Store metadata
            f.attrs['metadata'] = json.dumps(metadata.to_dict())
            
            # Create datasets
            velocity_data = f.create_dataset('velocity_magnitude',
                                           shape=(timesteps + 1, *metadata.grid_size),
                                           dtype=np.float32)
            pressure_data = f.create_dataset('pressure',
                                           shape=(timesteps + 1, *metadata.grid_size),
                                           dtype=np.float32)
            
            # Store initial state
            velocity_data[0] = solver.get_velocity_magnitude()
            pressure_data[0] = solver.p
            
            # Run simulation
            for t in range(timesteps):
                solver.step()
                velocity_data[t + 1] = solver.get_velocity_magnitude()
                pressure_data[t + 1] = solver.p
                
                if (t + 1) % 25 == 0:
                    print(f"  Generated {t + 1}/{timesteps} timesteps for {metadata.dataset_id}")
        
        # Save metadata
        metadata_path = self.output_dir / f"{metadata.dataset_id}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata.to_dict(), f, indent=2)
        
        return str(file_path)
    
    def _generate_rigid_time_series(self, solver: RigidBodySolver, metadata: DatasetMetadata,
                                  timesteps: int) -> str:
        """Generate rigid body dynamics time series data."""
        file_path = self.output_dir / f"{metadata.dataset_id}.h5"
        
        num_bodies = len(solver.bodies)
        
        with h5py.File(file_path, 'w') as f:
            # Store metadata
            f.attrs['metadata'] = json.dumps(metadata.to_dict())
            
            # Create datasets
            positions = f.create_dataset('positions',
                                       shape=(timesteps + 1, num_bodies, 3),
                                       dtype=np.float32)
            velocities = f.create_dataset('velocities',
                                        shape=(timesteps + 1, num_bodies, 3),
                                        dtype=np.float32)
            
            # Store initial state
            for i, body in enumerate(solver.bodies.values()):
                positions[0, i] = body.position
                velocities[0, i] = body.velocity
            
            # Run simulation
            for t in range(timesteps):
                solver.step()
                
                for i, body in enumerate(solver.bodies.values()):
                    positions[t + 1, i] = body.position
                    velocities[t + 1, i] = body.velocity
                
                if (t + 1) % 100 == 0:
                    print(f"  Generated {t + 1}/{timesteps} timesteps for {metadata.dataset_id}")
        
        # Save metadata
        metadata_path = self.output_dir / f"{metadata.dataset_id}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata.to_dict(), f, indent=2)
        
        return str(file_path)