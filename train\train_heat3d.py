"""
Training script for 3D heat equation surrogate model.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import h5py
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import argparse
from tqdm import tqdm

from models.unet3d import PhysicsUNet3D

class HeatDataset(Dataset):
    """Dataset for heat diffusion training data."""
    
    def __init__(self, data_files: List[str], sequence_length: int = 2):
        self.data_files = data_files
        self.sequence_length = sequence_length
        self.samples = []
        
        # Index all valid sequences
        for file_path in data_files:
            with h5py.File(file_path, 'r') as f:
                metadata = json.loads(f.attrs['metadata'])
                num_timesteps = metadata['num_timesteps']
                
                for start_idx in range(num_timesteps - sequence_length + 1):
                    self.samples.append((file_path, start_idx))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        file_path, start_idx = self.samples[idx]
        
        with h5py.File(file_path, 'r') as f:
            # Get sequence of temperature fields
            temp_data = f['temperature'][start_idx:start_idx + self.sequence_length]
            
            # Input: current field + boundary conditions + material properties
            current_field = temp_data[0]
            target_field = temp_data[1]
            
            # Create boundary mask (simplified - edges are boundaries)
            boundary_mask = np.zeros_like(current_field)
            boundary_mask[0, :, :] = 1
            boundary_mask[-1, :, :] = 1
            boundary_mask[:, 0, :] = 1
            boundary_mask[:, -1, :] = 1
            boundary_mask[:, :, 0] = 1
            boundary_mask[:, :, -1] = 1
            
            # Material map (uniform for now)
            material_map = np.ones_like(current_field)
            
            # Control field (no external control for now)
            control_field = np.zeros_like(current_field)
            
            # Stack input channels
            input_tensor = np.stack([
                current_field, boundary_mask, material_map, control_field
            ], axis=0)
            
            # Target is the change (delta)
            target_delta = target_field - current_field
            
            return {
                'input': torch.FloatTensor(input_tensor),
                'target': torch.FloatTensor(target_delta[None]),  # Add channel dim
                'boundary_mask': torch.FloatTensor(boundary_mask[None])
            }

def train_epoch(model: nn.Module, dataloader: DataLoader, optimizer: optim.Optimizer,
                device: torch.device) -> Dict[str, float]:
    """Train for one epoch."""
    model.train()
    total_loss = 0
    physics_losses = {'laplacian_loss': 0, 'conservation_loss': 0, 'boundary_loss': 0}
    
    for batch in tqdm(dataloader, desc="Training"):
        optimizer.zero_grad()
        
        inputs = batch['input'].to(device)
        targets = batch['target'].to(device)
        boundary_masks = batch['boundary_mask'].to(device)
        
        # Forward pass
        predictions, losses = model(inputs, boundary_masks)
        
        # Main prediction loss
        pred_loss = nn.MSELoss()(predictions, targets)
        
        # Total loss with physics constraints
        total_batch_loss = pred_loss
        for loss_name, loss_value in losses.items():
            total_batch_loss += loss_value
            physics_losses[loss_name] += loss_value.item()
        
        # Backward pass
        total_batch_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += total_batch_loss.item()
    
    # Average losses
    num_batches = len(dataloader)
    avg_loss = total_loss / num_batches
    for key in physics_losses:
        physics_losses[key] /= num_batches
    
    return {'total_loss': avg_loss, **physics_losses}

def validate(model: nn.Module, dataloader: DataLoader, device: torch.device) -> Dict[str, float]:
    """Validate model."""
    model.eval()
    total_loss = 0
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Validation"):
            inputs = batch['input'].to(device)
            targets = batch['target'].to(device)
            boundary_masks = batch['boundary_mask'].to(device)
            
            predictions, losses = model(inputs, boundary_masks)
            
            pred_loss = nn.MSELoss()(predictions, targets)
            total_loss += pred_loss.item()
    
    return {'val_loss': total_loss / len(dataloader)}

def main():
    parser = argparse.ArgumentParser(description='Train 3D Heat Surrogate Model')
    parser.add_argument('--data_dir', type=str, required=True, help='Directory with training data')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='checkpoints', help='Output directory')
    
    args = parser.parse_args()
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Find data files
    data_dir = Path(args.data_dir)
    data_files = list(data_dir.glob("heat_*.h5"))
    print(f"Found {len(data_files)} data files")
    
    if len(data_files) == 0:
        print("No data files found! Generate data first.")
        return
    
    # Split data
    train_files = data_files[:int(0.8 * len(data_files))]
    val_files = data_files[int(0.8 * len(data_files)):]
    
    # Create datasets
    train_dataset = HeatDataset(train_files)
    val_dataset = HeatDataset(val_files)
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Create model
    model = PhysicsUNet3D(
        in_channels=4,
        out_channels=1,
        base_channels=16,
        physics_type="heat"
    ).to(device)
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # Training loop
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        print(f"\nEpoch {epoch + 1}/{args.epochs}")
        
        # Train
        train_metrics = train_epoch(model, train_loader, optimizer, device)
        
        # Validate
        val_metrics = validate(model, val_loader, device)
        
        # Update scheduler
        scheduler.step(val_metrics['val_loss'])
        
        # Print metrics
        print(f"Train Loss: {train_metrics['total_loss']:.6f}")
        print(f"  - Laplacian: {train_metrics['laplacian_loss']:.6f}")
        print(f"  - Conservation: {train_metrics['conservation_loss']:.6f}")
        print(f"  - Boundary: {train_metrics['boundary_loss']:.6f}")
        print(f"Val Loss: {val_metrics['val_loss']:.6f}")
        print(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # Save best model
        if val_metrics['val_loss'] < best_val_loss:
            best_val_loss = val_metrics['val_loss']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': best_val_loss,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics
            }, output_dir / 'best_model.pth')
            print("Saved best model!")
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
            }, output_dir / f'checkpoint_epoch_{epoch + 1}.pth')
    
    print(f"\nTraining completed! Best validation loss: {best_val_loss:.6f}")

if __name__ == "__main__":
    main()