"""
Scene Inspector for NeoPhysics.

Displays scene hierarchy and allows property editing.
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem, 
                            QGroupBox, QFormLayout, QLineEdit, QDoubleSpinBox,
                            QPushButton, QLabel, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal

from core.scene import Scene, SceneObject, FieldData

class SceneInspector(QWidget):
    """Scene inspector widget for viewing and editing scene objects."""
    
    object_selected = pyqtSignal(str)  # object_id
    property_changed = pyqtSignal(str, str, object)  # object_id, property_name, value
    
    def __init__(self, scene: Scene):
        super().__init__()
        self.scene = scene
        self.current_object = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the scene inspector UI."""
        layout = QVBoxLayout(self)
        
        # Scene tree
        tree_group = QGroupBox("Scene Hierarchy")
        tree_layout = QVBoxLayout(tree_group)
        
        self.scene_tree = QTreeWidget()
        self.scene_tree.setHeaderLabels(["Name", "Type"])
        self.scene_tree.itemSelectionChanged.connect(self.on_selection_changed)
        tree_layout.addWidget(self.scene_tree)
        
        layout.addWidget(tree_group)
        
        # Properties panel
        props_group = QGroupBox("Properties")
        self.props_layout = QFormLayout(props_group)
        
        # Object name
        self.name_edit = QLineEdit()
        self.name_edit.textChanged.connect(lambda: self.update_property("name", self.name_edit.text()))
        self.props_layout.addRow("Name:", self.name_edit)
        
        # Position
        self.pos_x = QDoubleSpinBox()
        self.pos_x.setRange(-100, 100)
        self.pos_x.setSingleStep(0.1)
        self.pos_x.valueChanged.connect(self.update_position)
        
        self.pos_y = QDoubleSpinBox()
        self.pos_y.setRange(-100, 100)
        self.pos_y.setSingleStep(0.1)
        self.pos_y.valueChanged.connect(self.update_position)
        
        self.pos_z = QDoubleSpinBox()
        self.pos_z.setRange(-100, 100)
        self.pos_z.setSingleStep(0.1)
        self.pos_z.valueChanged.connect(self.update_position)
        
        pos_widget = QWidget()
        pos_layout = QVBoxLayout(pos_widget)
        pos_layout.addWidget(self.pos_x)
        pos_layout.addWidget(self.pos_y)
        pos_layout.addWidget(self.pos_z)
        self.props_layout.addRow("Position:", pos_widget)
        
        # Temperature
        self.temp_spin = QDoubleSpinBox()
        self.temp_spin.setRange(-273, 1000)
        self.temp_spin.setSuffix(" °C")
        self.temp_spin.valueChanged.connect(lambda: self.update_property("temperature", self.temp_spin.value()))
        self.props_layout.addRow("Temperature:", self.temp_spin)
        
        # Material
        self.material_combo = QComboBox()
        self.material_combo.addItems(["default_thermal", "copper", "steel", "insulator"])
        self.material_combo.currentTextChanged.connect(lambda: self.update_property("material", self.material_combo.currentText()))
        self.props_layout.addRow("Material:", self.material_combo)
        
        layout.addWidget(props_group)
        
        # Actions
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        delete_btn = QPushButton("Delete Object")
        delete_btn.clicked.connect(self.delete_current_object)
        actions_layout.addWidget(delete_btn)
        
        layout.addWidget(actions_group)
        
        # Initially disable properties
        self.set_properties_enabled(False)
    
    def refresh(self):
        """Refresh the scene tree."""
        self.scene_tree.clear()
        
        # Add objects
        objects_item = QTreeWidgetItem(["Objects", ""])
        self.scene_tree.addTopLevelItem(objects_item)
        
        for obj in self.scene.objects.values():
            obj_item = QTreeWidgetItem([obj.name, obj.object_type.value])
            obj_item.setData(0, Qt.ItemDataRole.UserRole, obj.id)
            objects_item.addChild(obj_item)
        
        # Add fields
        fields_item = QTreeWidgetItem(["Fields", ""])
        self.scene_tree.addTopLevelItem(fields_item)
        
        for field in self.scene.fields.values():
            field_item = QTreeWidgetItem([field.name, "field"])
            field_item.setData(0, Qt.ItemDataRole.UserRole, field.name)
            fields_item.addChild(field_item)
        
        # Expand all
        self.scene_tree.expandAll()
    
    def on_selection_changed(self):
        """Handle selection change in scene tree."""
        items = self.scene_tree.selectedItems()
        if not items:
            self.current_object = None
            self.set_properties_enabled(False)
            return
        
        item = items[0]
        object_id = item.data(0, Qt.ItemDataRole.UserRole)
        
        if object_id:
            obj = self.scene.get_object(object_id)
            if obj:
                self.current_object = obj
                self.load_object_properties(obj)
                self.set_properties_enabled(True)
                self.object_selected.emit(object_id)
            else:
                # Might be a field
                field = self.scene.get_field(object_id)
                if field:
                    self.current_object = None
                    self.set_properties_enabled(False)
        else:
            self.current_object = None
            self.set_properties_enabled(False)
    
    def load_object_properties(self, obj: SceneObject):
        """Load object properties into the UI."""
        # Block signals to avoid triggering updates
        self.name_edit.blockSignals(True)
        self.pos_x.blockSignals(True)
        self.pos_y.blockSignals(True)
        self.pos_z.blockSignals(True)
        self.temp_spin.blockSignals(True)
        self.material_combo.blockSignals(True)
        
        # Set values
        self.name_edit.setText(obj.name)
        self.pos_x.setValue(obj.transform.position[0])
        self.pos_y.setValue(obj.transform.position[1])
        self.pos_z.setValue(obj.transform.position[2])
        self.temp_spin.setValue(obj.properties.get("temperature", 20.0))
        
        if obj.material:
            index = self.material_combo.findText(obj.material.name)
            if index >= 0:
                self.material_combo.setCurrentIndex(index)
        
        # Unblock signals
        self.name_edit.blockSignals(False)
        self.pos_x.blockSignals(False)
        self.pos_y.blockSignals(False)
        self.pos_z.blockSignals(False)
        self.temp_spin.blockSignals(False)
        self.material_combo.blockSignals(False)
    
    def set_properties_enabled(self, enabled: bool):
        """Enable/disable property editing."""
        self.name_edit.setEnabled(enabled)
        self.pos_x.setEnabled(enabled)
        self.pos_y.setEnabled(enabled)
        self.pos_z.setEnabled(enabled)
        self.temp_spin.setEnabled(enabled)
        self.material_combo.setEnabled(enabled)
    
    def update_position(self):
        """Update object position."""
        if not self.current_object:
            return
        
        self.current_object.transform.position[0] = self.pos_x.value()
        self.current_object.transform.position[1] = self.pos_y.value()
        self.current_object.transform.position[2] = self.pos_z.value()
        
        self.property_changed.emit(self.current_object.id, "position", 
                                 self.current_object.transform.position.copy())
    
    def update_property(self, prop_name: str, value):
        """Update object property."""
        if not self.current_object:
            return
        
        if prop_name == "name":
            self.current_object.name = value
        elif prop_name == "temperature":
            self.current_object.properties["temperature"] = value
        elif prop_name == "material":
            material = self.scene.materials.get(value)
            if material:
                self.current_object.material = material
        
        self.property_changed.emit(self.current_object.id, prop_name, value)
    
    def delete_current_object(self):
        """Delete the currently selected object."""
        if self.current_object:
            self.scene.remove_object(self.current_object.id)
            self.current_object = None
            self.refresh()
            self.set_properties_enabled(False)