"""
3D Rigid Body Physics Solver for NeoPhysics.

Implements basic rigid body dynamics with gravity, collisions, and constraints.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum

class BodyType(Enum):
    """Types of rigid bodies."""
    DYNAMIC = "dynamic"  # Affected by forces
    STATIC = "static"    # Fixed in place
    KINEMATIC = "kinematic"  # Moves but not affected by forces

@dataclass
class RigidBody:
    """A rigid body with physics properties."""
    id: str
    name: str
    body_type: BodyType = BodyType.DYNAMIC
    
    # Transform
    position: np.ndarray = field(default_factory=lambda: np.zeros(3))
    rotation: np.ndarray = field(default_factory=lambda: np.zeros(4))  # quaternion [w,x,y,z]
    
    # Physics properties
    mass: float = 1.0
    radius: float = 0.5  # For sphere collision
    restitution: float = 0.6  # Bounciness
    friction: float = 0.3
    
    # State
    velocity: np.ndarray = field(default_factory=lambda: np.zeros(3))
    angular_velocity: np.ndarray = field(default_factory=lambda: np.zeros(3))
    force: np.ndarray = field(default_factory=lambda: np.zeros(3))
    torque: np.ndarray = field(default_factory=lambda: np.zeros(3))
    
    def __post_init__(self):
        """Initialize quaternion to identity."""
        if np.allclose(self.rotation, 0):
            self.rotation = np.array([1.0, 0.0, 0.0, 0.0])  # Identity quaternion

class RigidBodySolver:
    """3D rigid body physics solver."""
    
    def __init__(self, gravity: float = 9.81):
        self.gravity = gravity
        self.bodies: Dict[str, RigidBody] = {}
        self.time = 0.0
        self.dt = 1.0 / 60.0  # 60 FPS
        
        # Ground plane
        self.ground_y = 0.0
        self.ground_enabled = True
    
    def add_body(self, body: RigidBody) -> str:
        """Add a rigid body to the simulation."""
        self.bodies[body.id] = body
        return body.id
    
    def remove_body(self, body_id: str) -> bool:
        """Remove a body from simulation."""
        if body_id in self.bodies:
            del self.bodies[body_id]
            return True
        return False
    
    def get_body(self, body_id: str) -> Optional[RigidBody]:
        """Get a body by ID."""
        return self.bodies.get(body_id)
    
    def set_gravity(self, gravity: float):
        """Set gravity acceleration."""
        self.gravity = gravity
    
    def set_ground_level(self, y: float):
        """Set ground plane Y level."""
        self.ground_y = y
    
    def step(self, dt: Optional[float] = None):
        """Advance simulation by one time step."""
        if dt is None:
            dt = self.dt
        
        # Apply forces
        self._apply_forces()
        
        # Integrate motion
        self._integrate(dt)
        
        # Handle collisions
        self._handle_collisions()
        
        # Update time
        self.time += dt
    
    def _apply_forces(self):
        """Apply forces to all bodies."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue
            
            # Clear previous forces
            body.force.fill(0)
            body.torque.fill(0)
            
            # Apply gravity
            body.force[1] -= body.mass * self.gravity
    
    def _integrate(self, dt: float):
        """Integrate equations of motion."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue
            
            # Linear motion (Euler integration)
            acceleration = body.force / body.mass
            body.velocity += acceleration * dt
            body.position += body.velocity * dt
            
            # Angular motion (simplified)
            # For spheres, moment of inertia I = (2/5) * m * r^2
            inertia = (2.0/5.0) * body.mass * body.radius**2
            if inertia > 0:
                angular_acceleration = body.torque / inertia
                body.angular_velocity += angular_acceleration * dt
                
                # Update rotation (simplified quaternion integration)
                angle = np.linalg.norm(body.angular_velocity) * dt
                if angle > 1e-6:
                    axis = body.angular_velocity / np.linalg.norm(body.angular_velocity)
                    dq = self._axis_angle_to_quaternion(axis, angle)
                    body.rotation = self._multiply_quaternions(body.rotation, dq)
                    body.rotation = body.rotation / np.linalg.norm(body.rotation)
    
    def _handle_collisions(self):
        """Handle collisions with ground and other objects."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue
            
            # Ground collision
            if self.ground_enabled:
                ground_contact = body.position[1] - body.radius
                if ground_contact <= self.ground_y:
                    # Resolve penetration
                    body.position[1] = self.ground_y + body.radius
                    
                    # Apply collision response
                    if body.velocity[1] < 0:  # Moving downward
                        body.velocity[1] = -body.velocity[1] * body.restitution
                        
                        # Apply friction
                        horizontal_vel = np.array([body.velocity[0], 0, body.velocity[2]])
                        if np.linalg.norm(horizontal_vel) > 1e-6:
                            friction_force = -horizontal_vel * body.friction
                            body.velocity[0] += friction_force[0] * self.dt
                            body.velocity[2] += friction_force[2] * self.dt
        
        # Object-object collisions (simplified sphere-sphere)
        body_list = list(self.bodies.values())
        for i in range(len(body_list)):
            for j in range(i + 1, len(body_list)):
                body1, body2 = body_list[i], body_list[j]
                
                if (body1.body_type != BodyType.DYNAMIC and 
                    body2.body_type != BodyType.DYNAMIC):
                    continue
                
                # Check collision
                distance = np.linalg.norm(body1.position - body2.position)
                min_distance = body1.radius + body2.radius
                
                if distance < min_distance and distance > 1e-6:
                    # Collision normal
                    normal = (body2.position - body1.position) / distance
                    
                    # Resolve penetration
                    penetration = min_distance - distance
                    if body1.body_type == BodyType.DYNAMIC:
                        body1.position -= normal * penetration * 0.5
                    if body2.body_type == BodyType.DYNAMIC:
                        body2.position += normal * penetration * 0.5
                    
                    # Apply collision response
                    relative_velocity = body2.velocity - body1.velocity
                    velocity_along_normal = np.dot(relative_velocity, normal)
                    
                    if velocity_along_normal > 0:
                        continue  # Objects separating
                    
                    # Calculate restitution
                    restitution = min(body1.restitution, body2.restitution)
                    
                    # Calculate impulse
                    impulse_magnitude = -(1 + restitution) * velocity_along_normal
                    if body1.body_type == BodyType.DYNAMIC and body2.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body1.mass + 1/body2.mass)
                    elif body1.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body1.mass)
                    elif body2.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body2.mass)
                    
                    impulse = impulse_magnitude * normal
                    
                    # Apply impulse
                    if body1.body_type == BodyType.DYNAMIC:
                        body1.velocity -= impulse / body1.mass
                    if body2.body_type == BodyType.DYNAMIC:
                        body2.velocity += impulse / body2.mass
    
    def _axis_angle_to_quaternion(self, axis: np.ndarray, angle: float) -> np.ndarray:
        """Convert axis-angle to quaternion."""
        half_angle = angle * 0.5
        sin_half = np.sin(half_angle)
        cos_half = np.cos(half_angle)
        return np.array([cos_half, axis[0]*sin_half, axis[1]*sin_half, axis[2]*sin_half])
    
    def _multiply_quaternions(self, q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
        """Multiply two quaternions."""
        w1, x1, y1, z1 = q1
        w2, x2, y2, z2 = q2
        return np.array([
            w1*w2 - x1*x2 - y1*y2 - z1*z2,
            w1*x2 + x1*w2 + y1*z2 - z1*y2,
            w1*y2 - x1*z2 + y1*w2 + z1*x2,
            w1*z2 + x1*y2 - y1*x2 + z1*w2
        ])
    
    def get_stats(self) -> Dict[str, float]:
        """Get simulation statistics."""
        total_energy = 0.0
        total_momentum = np.zeros(3)
        
        for body in self.bodies.values():
            if body.body_type == BodyType.DYNAMIC:
                # Kinetic energy
                ke = 0.5 * body.mass * np.dot(body.velocity, body.velocity)
                # Potential energy
                pe = body.mass * self.gravity * body.position[1]
                total_energy += ke + pe
                
                # Momentum
                total_momentum += body.mass * body.velocity
        
        return {
            'time': self.time,
            'total_energy': total_energy,
            'momentum_magnitude': np.linalg.norm(total_momentum),
            'num_bodies': len(self.bodies)
        }
    
    def reset(self):
        """Reset simulation."""
        self.time = 0.0
        for body in self.bodies.values():
            body.velocity.fill(0)
            body.angular_velocity.fill(0)
            body.force.fill(0)
            body.torque.fill(0)