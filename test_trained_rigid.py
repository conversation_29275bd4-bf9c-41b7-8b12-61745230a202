#!/usr/bin/env python3
"""
Test trained rigid body neural network.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

import torch
import numpy as np
from train.train_rigid3d import RigidBodyNet

def test_trained_model():
    """Test the trained rigid body model."""
    print("TESTING TRAINED RIGID BODY MODEL")
    print("=" * 50)
    
    # Load trained model
    print("\n1. Loading trained model...")
    model = RigidBodyNet(input_dim=7, hidden_dim=128, output_dim=6)
    
    try:
        checkpoint = torch.load('checkpoints/best_rigid_model.pth', map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print(f"[OK] Model loaded successfully!")
        print(f"  - Training loss: {checkpoint['train_metrics']['total_loss']:.6f}")
        print(f"  - Validation loss: {checkpoint['val_loss']:.6f}")
        print(f"  - Epoch: {checkpoint['epoch'] + 1}")
        
    except FileNotFoundError:
        print("[ERROR] Model not found! Train the model first.")
        return
    
    # Test inference
    print(f"\n2. Testing inference...")
    
    # Create test input: 2 balls at different heights
    batch_size = 1
    max_bodies = 5
    
    # Ball 1: at height 3m, no initial velocity
    # Ball 2: at height 2m, small horizontal velocity
    test_input = torch.zeros(batch_size, max_bodies, 7)
    
    # Ball 1
    test_input[0, 0, :] = torch.tensor([0, 3, 0,  # position
                                       0, 0, 0,   # velocity  
                                       1.0])      # mass
    
    # Ball 2  
    test_input[0, 1, :] = torch.tensor([1, 2, 0,  # position
                                       0.5, 0, 0, # velocity
                                       2.0])      # mass
    
    print(f"Input shape: {test_input.shape}")
    print(f"Ball 1: pos=[0,3,0], vel=[0,0,0], mass=1.0")
    print(f"Ball 2: pos=[1,2,0], vel=[0.5,0,0], mass=2.0")
    
    # Run inference
    with torch.no_grad():
        prediction = model(test_input)
    
    print(f"\nPrediction shape: {prediction.shape}")
    print(f"Ball 1 deltas: pos={prediction[0,0,:3].numpy()}, vel={prediction[0,0,3:].numpy()}")
    print(f"Ball 2 deltas: pos={prediction[0,1,:3].numpy()}, vel={prediction[0,1,3:].numpy()}")
    
    # Test physics consistency
    print(f"\n3. Physics consistency check...")
    
    # Check if gravity is applied (negative y-velocity change)
    ball1_vel_delta = prediction[0, 0, 4].item()  # y-velocity delta
    ball2_vel_delta = prediction[0, 1, 4].item()  # y-velocity delta
    
    if ball1_vel_delta < 0 and ball2_vel_delta < 0:
        print("[OK] Gravity effect detected (negative y-velocity deltas)")
    else:
        print("[?] Gravity effect unclear")
    
    # Test rollout
    print(f"\n4. Testing multi-step rollout...")
    
    current_state = test_input.clone()
    trajectory = [current_state[0, :2, :3].clone()]  # Store positions of first 2 bodies
    
    with torch.no_grad():
        for step in range(10):
            delta = model(current_state)
            
            # Update positions and velocities
            current_state[:, :, :3] += delta[:, :, :3]  # Update positions
            current_state[:, :, 3:6] += delta[:, :, 3:6]  # Update velocities
            
            trajectory.append(current_state[0, :2, :3].clone())
    
    print(f"Generated {len(trajectory)} timesteps")
    print(f"Ball 1 final position: {trajectory[-1][0].numpy()}")
    print(f"Ball 2 final position: {trajectory[-1][1].numpy()}")
    
    # Check if balls fell
    initial_y1 = trajectory[0][0, 1].item()
    final_y1 = trajectory[-1][0, 1].item()
    
    if final_y1 < initial_y1:
        print(f"[OK] Ball 1 fell from {initial_y1:.2f}m to {final_y1:.2f}m")
    else:
        print(f"[?] Ball 1 motion unclear")
    
    print(f"\n[SUCCESS] Trained rigid body model working!")
    print("The neural network can predict rigid body dynamics!")

if __name__ == "__main__":
    test_trained_model()