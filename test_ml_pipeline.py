#!/usr/bin/env python3
"""
Test ML Pipeline for NeoPhysics.

Demonstrates the complete pipeline: data generation -> model training -> inference.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

import numpy as np
import torch
from pathlib import Path

from data.generator import PhysicsDataGenerator
from models.unet3d import PhysicsUNet3D
from train.train_heat3d import HeatDataset

def test_data_generation():
    """Test physics data generation."""
    print("=" * 60)
    print("TESTING PHYSICS DATA GENERATION")
    print("=" * 60)
    
    # Create output directory
    output_dir = Path("test_data")
    output_dir.mkdir(exist_ok=True)
    
    # Create generator
    generator = PhysicsDataGenerator(output_dir)
    
    print("\n1. Generating Heat Diffusion Data...")
    heat_files = generator.generate_heat_dataset(
        grid_sizes=[(16, 16, 16)],  # Small for testing
        num_scenarios=2,
        timesteps=50
    )
    print(f"Generated {len(heat_files)} heat datasets")
    
    print("\n2. Generating Fluid Dynamics Data...")
    fluid_files = generator.generate_fluid_dataset(
        grid_sizes=[(16, 16, 16)],
        num_scenarios=1,
        timesteps=25
    )
    print(f"Generated {len(fluid_files)} fluid datasets")
    
    print("\n3. Generating Rigid Body Data...")
    rigid_files = generator.generate_rigid_dataset(
        num_scenarios=2,
        timesteps=100
    )
    print(f"Generated {len(rigid_files)} rigid body datasets")
    
    return heat_files, fluid_files, rigid_files

def test_model_architecture():
    """Test neural model architectures."""
    print("\n" + "=" * 60)
    print("TESTING NEURAL MODEL ARCHITECTURES")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test 3D U-Net
    print("\n1. Testing 3D U-Net...")
    model = PhysicsUNet3D(
        in_channels=4,
        out_channels=1,
        base_channels=8,  # Small for testing
        physics_type="heat"
    ).to(device)
    
    # Test input
    batch_size = 2
    grid_size = (16, 16, 16)
    
    x = torch.randn(batch_size, 4, *grid_size).to(device)
    boundary_mask = torch.zeros(batch_size, 1, *grid_size).to(device)
    
    with torch.no_grad():
        pred, losses = model(x, boundary_mask)
    
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {pred.shape}")
    print(f"Physics losses: {list(losses.keys())}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    return model

def test_training_pipeline(heat_files):
    """Test training pipeline."""
    print("\n" + "=" * 60)
    print("TESTING TRAINING PIPELINE")
    print("=" * 60)
    
    if not heat_files:
        print("No heat data files available for training test")
        return
    
    # Create dataset
    print("\n1. Creating dataset...")
    dataset = HeatDataset(heat_files, sequence_length=2)
    print(f"Dataset size: {len(dataset)}")
    
    if len(dataset) == 0:
        print("Dataset is empty!")
        return
    
    # Test data loading
    print("\n2. Testing data loading...")
    sample = dataset[0]
    print(f"Input shape: {sample['input'].shape}")
    print(f"Target shape: {sample['target'].shape}")
    print(f"Boundary mask shape: {sample['boundary_mask'].shape}")
    
    # Create data loader
    from torch.utils.data import DataLoader
    dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
    
    # Test model training step
    print("\n3. Testing training step...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = PhysicsUNet3D(
        in_channels=4,
        out_channels=1,
        base_channels=8,
        physics_type="heat"
    ).to(device)
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # Single training step
    model.train()
    for batch in dataloader:
        optimizer.zero_grad()
        
        inputs = batch['input'].to(device)
        targets = batch['target'].to(device)
        boundary_masks = batch['boundary_mask'].to(device)
        
        predictions, losses = model(inputs, boundary_masks)
        
        # Compute total loss
        pred_loss = torch.nn.MSELoss()(predictions, targets)
        total_loss = pred_loss
        for loss_value in losses.values():
            total_loss += loss_value
        
        total_loss.backward()
        optimizer.step()
        
        print(f"Prediction loss: {pred_loss.item():.6f}")
        print(f"Physics losses: {[(k, v.item()) for k, v in losses.items()]}")
        print(f"Total loss: {total_loss.item():.6f}")
        
        break  # Only test one batch
    
    print("Training step successful!")

def test_inference():
    """Test model inference."""
    print("\n" + "=" * 60)
    print("TESTING MODEL INFERENCE")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model
    model = PhysicsUNet3D(
        in_channels=4,
        out_channels=1,
        base_channels=8,
        physics_type="heat"
    ).to(device)
    
    model.eval()
    
    # Test inference speed
    grid_size = (32, 32, 32)
    x = torch.randn(1, 4, *grid_size).to(device)
    
    print(f"\n1. Testing inference speed on {grid_size} grid...")
    
    # Warmup
    with torch.no_grad():
        for _ in range(5):
            _ = model.unet(x)
    
    # Time inference
    import time
    torch.cuda.synchronize() if device.type == 'cuda' else None
    
    start_time = time.time()
    with torch.no_grad():
        for _ in range(10):
            pred = model.unet(x)
    
    torch.cuda.synchronize() if device.type == 'cuda' else None
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"Average inference time: {avg_time*1000:.2f} ms")
    print(f"Throughput: {1/avg_time:.1f} FPS")
    
    # Test autoregressive rollout
    print(f"\n2. Testing autoregressive rollout...")
    
    current_field = torch.randn(1, 1, *grid_size).to(device)
    boundary_mask = torch.zeros(1, 1, *grid_size).to(device)
    material_map = torch.ones(1, 1, *grid_size).to(device)
    control_field = torch.zeros(1, 1, *grid_size).to(device)
    
    rollout_steps = 10
    trajectory = [current_field.cpu().numpy()]
    
    with torch.no_grad():
        for step in range(rollout_steps):
            # Prepare input
            input_tensor = torch.cat([
                current_field, boundary_mask, material_map, control_field
            ], dim=1)
            
            # Predict next step
            delta, _ = model(input_tensor, boundary_mask)
            current_field = current_field + delta
            
            trajectory.append(current_field.cpu().numpy())
    
    print(f"Generated {len(trajectory)} timesteps in rollout")
    print(f"Field range: [{np.min(trajectory[-1]):.3f}, {np.max(trajectory[-1]):.3f}]")
    
    return trajectory

def main():
    """Run complete ML pipeline test."""
    print("NEOPHYSICS ML PIPELINE TEST")
    print("Testing complete machine learning pipeline for physics simulation")
    
    try:
        # 1. Data Generation
        heat_files, fluid_files, rigid_files = test_data_generation()
        
        # 2. Model Architecture
        model = test_model_architecture()
        
        # 3. Training Pipeline
        test_training_pipeline(heat_files)
        
        # 4. Inference
        trajectory = test_inference()
        
        print("\n" + "=" * 60)
        print("ML PIPELINE TEST SUMMARY")
        print("=" * 60)
        print("[OK] Data generation - Heat, Fluid, Rigid body datasets")
        print("[OK] Neural architectures - 3D U-Net with physics constraints")
        print("[OK] Training pipeline - Dataset, DataLoader, training step")
        print("[OK] Inference - Single step and autoregressive rollout")
        print(f"[OK] Generated {len(trajectory)} timestep trajectory")
        
        print(f"\n[SUCCESS] Complete ML pipeline test PASSED!")
        print("Ready for full-scale training and deployment!")
        
    except Exception as e:
        print(f"\n[ERROR] ML pipeline test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()