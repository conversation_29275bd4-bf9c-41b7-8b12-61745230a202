# NeoPhysics Quick Start

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Gemini API** (optional):
   ```bash
   set GEMINI_API_KEY=your-api-key-here
   ```

## Running the Application

```bash
python main.py
```

## Basic Usage

### Natural Language Commands

Try these commands in the command panel:

**Create Objects:**
- `Place a hot cube at center with temperature 100`
- `Add a cold sphere at position 2,0,0 with temperature 0`
- `Create a copper cube with size 0.5`

**Setup Simulation:**
- `Create temperature field with grid size 32,32,32`
- `Run simulation for 100 steps`

**Scene Management:**
- `Clear the scene`
- `Show scene summary`

### UI Components

- **3D Viewport**: Interactive visualization with PyVista
- **Command Panel**: Natural language input (right panel)
- **Scene Inspector**: Object properties (left panel)
- **Timeline**: Simulation controls (bottom)

### Keyboard Shortcuts

- `Ctrl+Enter`: Execute command
- `Ctrl+Up/Down`: Navigate command history
- `Space`: Run simulation
- `Home`: Reset camera view

## Current Features

✅ **Implemented:**
- 3D heat diffusion simulation
- Natural language command parsing
- Interactive 3D visualization
- Object creation (cubes, spheres)
- Temperature field visualization
- Real-time simulation

🔄 **Next Steps:**
- Fluid dynamics solver
- Rigid body physics
- Neural surrogate models
- Advanced materials

## Example Workflow

1. Start the application: `python main.py`
2. Create objects: `Place a hot cube at center`
3. Add field: `Create temperature field`
4. Run simulation: `Run simulation for 200 steps`
5. Observe heat diffusion in the 3D viewport

## Troubleshooting

- **Import errors**: Ensure all dependencies are installed
- **No 3D display**: Check PyVista/VTK installation
- **Command parsing fails**: Verify Gemini API key or use simple commands