"""
Natural Language Parser for NeoPhysics.

Converts natural language commands into structured actions using Google Gemini API.
"""

import json
import re
import requests
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

@dataclass
class Action:
    """Represents a parsed action from natural language."""
    type: str  # add_object, set_property, run_simulation, etc.
    parameters: Dict[str, Any]
    confidence: float = 1.0
    raw_command: str = ""

class NLPParser:
    """Natural Language Parser using Google Gemini API."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gemini-1.5-flash"):
        """Initialize the parser with Gemini API."""
        self.api_key = api_key
        self.model = model
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        self.action_schema = self._get_action_schema()
    
    def _get_action_schema(self) -> Dict[str, Any]:
        """Define the JSON schema for actions."""
        return {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "string",
                                "enum": [
                                    "add_ball", "add_sphere", "add_cube", "add_plane",
                                    "set_position", "set_velocity", "set_mass",
                                    "run_physics", "clear_scene", "query_scene"
                                ]
                            },
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "position": {
                                        "type": "array",
                                        "items": {"type": "number"},
                                        "minItems": 3,
                                        "maxItems": 3
                                    },
                                    "size": {"type": "number"},
                                    "radius": {"type": "number"},
                                    "mass": {"type": "number"},
                                    "velocity": {
                                        "type": "array",
                                        "items": {"type": "number"},
                                        "minItems": 3,
                                        "maxItems": 3
                                    },
                                    "height": {"type": "number"},
                                    "physics_mode": {"type": "string"},
                                    "steps": {"type": "integer"},
                                    "duration": {"type": "number"},
                                    "grid_size": {
                                        "type": "array",
                                        "items": {"type": "integer"},
                                        "minItems": 3,
                                        "maxItems": 3
                                    }
                                }
                            },
                            "confidence": {"type": "number", "minimum": 0, "maximum": 1}
                        },
                        "required": ["type", "parameters"]
                    }
                }
            },
            "required": ["actions"]
        }
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the OpenAI API."""
        return """You are a natural language parser for a 3D physics simulation environment similar to Blender. 
Your job is to convert user commands into structured JSON actions.

Available actions:
- add_ball: Create a physics ball (parameters: name, position, radius, mass, velocity, height)
- add_sphere: Create a sphere object (parameters: name, position, radius)
- add_cube: Create a cube object (parameters: name, position, size)
- add_plane: Create a plane object (parameters: name, position, size)
- set_position: Move an object (parameters: name, position)
- set_velocity: Set velocity of an object (parameters: name, velocity)
- set_mass: Set mass of an object (parameters: name, mass)
- run_physics: Run rigid body physics simulation
- clear_scene: Clear all objects from scene
- query_scene: Get information about the scene

Materials available: default_thermal, copper, steel, insulator

Units:
- Position: meters (default origin at 0,0,0)
- Temperature: Celsius
- Size/radius: meters
- Time: seconds

Examples:
"Create a ball 2m high" -> add_ball with position [0,2,0]
"Create a sphere at position 2,1,0 with radius 0.5" -> add_sphere
"Run physics" -> run_physics
"Set velocity of ball to 5,0,0" -> set_velocity

Always return valid JSON with an "actions" array. Include confidence scores."""

    def parse(self, command: str) -> List[Action]:
        """Parse a natural language command into actions."""
        if not self.api_key or self.api_key == 'demo_mode':
            return self._rule_based_parse(command)
        
        try:
            url = f"{self.base_url}/{self.model}:generateContent?key={self.api_key}"
            
            prompt = f"{self._get_system_prompt()}\n\nUser command: {command}\n\nReturn only valid JSON with actions array."
            
            payload = {
                "contents": [{
                    "parts": [{"text": prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 1000
                }
            }
            
            response = requests.post(url, json=payload, headers={"Content-Type": "application/json"})
            response.raise_for_status()
            
            result = response.json()
            text = result["candidates"][0]["content"]["parts"][0]["text"]
            
            # Extract JSON from response
            json_start = text.find('{')
            json_end = text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_text = text[json_start:json_end]
                parsed = json.loads(json_text)
                
                actions = []
                for action_data in parsed.get("actions", []):
                    action = Action(
                        type=action_data["type"],
                        parameters=action_data["parameters"],
                        confidence=action_data.get("confidence", 1.0),
                        raw_command=command
                    )
                    actions.append(action)
                
                return actions
        
        except Exception as e:
            print(f"Gemini parsing failed: {e}")
            return self._rule_based_parse(command)
        
        return []
    
    def _rule_based_parse(self, command: str) -> List[Action]:
        """Fallback rule-based parser for basic commands."""
        command = command.lower().strip()
        actions = []
        
        # Simple pattern matching for physics commands
        if "cube" in command and ("add" in command or "create" in command or "place" in command):
            params = {"name": "cube", "position": [0, 0, 0], "size": 1.0}
            
            # Extract position
            pos_match = re.search(r'(?:at|position)\s*(?:\[?\s*)?(-?\d+(?:\.\d+)?)\s*,?\s*(-?\d+(?:\.\d+)?)\s*,?\s*(-?\d+(?:\.\d+)?)', command)
            if pos_match:
                params["position"] = [float(pos_match.group(1)), float(pos_match.group(2)), float(pos_match.group(3))]
            
            # Extract size
            size_match = re.search(r'size\s*(\d+(?:\.\d+)?)', command)
            if size_match:
                params["size"] = float(size_match.group(1))
            
            actions.append(Action("add_cube", params, 0.8, command))
        
        elif ("ball" in command or "sphere" in command) and ("add" in command or "create" in command or "place" in command):
            params = {"name": "ball", "position": [0, 0, 0], "radius": 0.1, "mass": 1.0}
            
            # Extract position
            pos_match = re.search(r'(?:at|position)\s*(?:\[?\s*)?(-?\d+(?:\.\d+)?)\s*,?\s*(-?\d+(?:\.\d+)?)\s*,?\s*(-?\d+(?:\.\d+)?)', command)
            if pos_match:
                params["position"] = [float(pos_match.group(1)), float(pos_match.group(2)), float(pos_match.group(3))]
            
            # Extract height
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*m?\s*high', command)
            if height_match:
                height = float(height_match.group(1))
                params["position"] = [0, height, 0]
                params["height"] = height
            
            # Extract radius
            radius_match = re.search(r'radius\s*(\d+(?:\.\d+)?)', command)
            if radius_match:
                params["radius"] = float(radius_match.group(1))
            
            # Extract mass
            mass_match = re.search(r'mass\s*(\d+(?:\.\d+)?)', command)
            if mass_match:
                params["mass"] = float(mass_match.group(1))
            
            actions.append(Action("add_ball", params, 0.9, command))
        
        elif "run" in command and ("physics" in command):
            params = {}
            actions.append(Action("run_physics", params, 0.9, command))
        

        
        elif "clear" in command:
            actions.append(Action("clear_scene", {}, 0.9, command))
        

        
        return actions

# Global parser instance
_parser_instance = None

def get_parser(api_key: Optional[str] = None) -> NLPParser:
    """Get the global parser instance."""
    global _parser_instance
    if _parser_instance is None:
        _parser_instance = NLPParser(api_key=api_key)
    return _parser_instance

def parse_command(command: str, api_key: Optional[str] = None) -> List[Action]:
    """Parse a command using the global parser."""
    parser = get_parser(api_key)
    return parser.parse(command)