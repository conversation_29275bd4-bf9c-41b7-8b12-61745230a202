# NeoPhysics

A Blender-like 3D Physics Sandbox with Natural Language Interface

## Overview

NeoPhysics is an interactive 3D physics simulation environment that allows users to create scenes and run physics simulations using natural language commands. Built with PyQt6, PyVista, and PyTorch, it provides an intuitive interface for exploring physics phenomena.

## Features

### Current (Phase 1 + 2)
- **3D Interactive Environment**: PyQt6-based GUI with 3D viewport using PyVista
- **Natural Language Interface**: Command parsing using Gemini API with rule-based fallback
- **Scene Management**: Object creation, materials, and properties
- **3D Heat Simulation**: Finite difference heat equation solver
- **High-Fidelity Rigid Body Physics**: Advanced physics with proper inertia tensors, contact manifolds, constraint-based solving, and multiple geometry types
- **Fluid Dynamics**: Incompressible Navier-Stokes solver with MAC grid
- **Neural Surrogate Models**: 3D U-Net with physics constraints for ML-accelerated simulation
- **Physics Data Generator**: Automated training data generation for heat, fluid, and rigid body domains
- **Real-time Visualization**: Temperature fields and physics objects with live updates
- **Physics Controls**: Interactive gravity slider, play/pause, and simulation parameters
- **ML Training Pipeline**: Complete training infrastructure with HDF5 data format

### Enhanced Rigid Body Physics (NEW!)
- **Proper Inertia Tensors**: 3x3 inertia tensors for realistic rotational dynamics
- **Multiple Geometry Types**: Spheres, boxes, and convex hulls with accurate collision detection
- **Contact Manifolds**: Persistent contact data with warm starting for stable simulations
- **Constraint-Based Solving**: Iterative projected Gauss-Seidel solver with friction
- **Symplectic Integration**: Semi-implicit integration for energy conservation
- **Enhanced Statistics**: Comprehensive energy, momentum, and contact monitoring
- **Natural Language Interface**: Create physics objects with "Create a physics sphere..." commands

### Planned (Phase 3)
- **Advanced Collision Detection**: GJK+EPA for general convex shapes, BVH for broad phase
- **Continuous Collision Detection**: Time-of-impact calculation for high-speed objects
- **Advanced Fluid Dynamics**: Full MAC grid implementation with proper pressure projection
- **Coupled Multi-Physics**: Heat-fluid and fluid-rigid coupling
- **Advanced Neural Models**: Graph networks for rigid bodies, diffusion models for uncertainty
- **Production Deployment**: TorchScript/ONNX export, optimized inference
- **Blender Integration**: Native Blender addon version

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd neophysics
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up OpenAI API** (optional, for advanced NLP):
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```

## Quick Start

### Running the Application
```bash
python main.py
```

### Basic Commands
Try these natural language commands in the command panel:

**Object Creation:**
- "Place a cube at the center"
- "Add a hot sphere at position 2,0,0"
- "Create a copper cube with size 0.5"
- "Create a ball 2m high"
- "Add a physics ball with mass 2kg"

**Temperature Control:**
- "Set temperature of cube1 to 100 degrees"
- "Make the sphere hot"

**Heat Simulation:**
- "Create temperature field"
- "Run simulation for 100 steps"
- "Simulate for 5 seconds"

**Physics Simulation:**
- "Run physics"
- "Create a ball 2m high"
- "Set gravity to 3.71" (Mars gravity)
- "Set velocity of ball to 5,0,0"

**Enhanced Physics Objects:**
- "Create a physics sphere at position 0,2,0 with radius 0.3 and mass 1.5"
- "Add a physics box at position 1,3,0 with dimensions 0.8,0.6,1.0 and mass 2.0"
- "Create a physics sphere with mass 2kg and radius 0.4"

**ML Pipeline:**
- Generate training data: `python -c "from data.generator import *; g=PhysicsDataGenerator('data'); g.generate_heat_dataset()"`
- Train surrogate model: `python train/train_heat3d.py --data_dir data --epochs 50`
- Test ML pipeline: `python test_ml_pipeline.py`

**Scene Management:**
- "Clear the scene"
- "Show scene summary"

### Testing
Run the basic test suite:
```bash
python test_basic.py
```

## Architecture

### Core Components

- **`core/scene.py`**: Scene graph management, objects, materials
- **`sim/heat3d.py`**: 3D heat equation solver
- **`nlp/parser.py`**: Natural language command parsing
- **`ui/`**: PyQt6-based user interface components

### UI Components

- **Main Window**: Application shell with menus and layout
- **3D Viewport**: Interactive PyVista-based 3D visualization
- **Command Panel**: Natural language input with history
- **Scene Inspector**: Object hierarchy and property editing
- **Timeline**: Simulation playback controls

## Usage Examples

### Creating a Heat Transfer Scene

```python
# Via natural language:
"Place a hot copper cube at center with temperature 100"
"Add a cold steel sphere at position 2,0,0 with temperature 0"
"Create temperature field with grid size 64,64,64"
"Run simulation for 200 steps"
```

### Programmatic Usage

```python
from core.scene import Scene
from sim.heat3d import Heat3DSolver

# Create scene
scene = Scene("Heat Transfer Demo")
scene.create_cube(position=(0,0,0), temperature=100.0, material="copper")
scene.create_temperature_field(grid_size=(32,32,32))

# Setup solver
solver = Heat3DSolver(grid_size=(32,32,32))
# ... configure and run simulation
```

## Development

### Project Structure
```
neophysics/
├── main.py              # Application entry point
├── config.py            # Configuration settings
├── requirements.txt     # Dependencies
├── core/               # Core physics and scene management
│   ├── scene.py        # Scene graph and objects
│   └── __init__.py
├── sim/                # Physics solvers
│   ├── heat3d.py       # 3D heat equation solver
│   └── __init__.py
├── nlp/                # Natural language processing
│   ├── parser.py       # Command parsing
│   └── __init__.py
├── ui/                 # User interface
│   ├── app.py          # Main application
│   ├── viewport.py     # 3D visualization
│   ├── command_panel.py # NLP interface
│   ├── scene_inspector.py # Object properties
│   ├── timeline.py     # Simulation controls
│   └── __init__.py
└── tests/              # Test files
```

### Adding New Physics Domains

1. Create solver in `sim/` (e.g., `fluids3d.py`)
2. Add object types and materials in `core/scene.py`
3. Update NLP parser with new commands
4. Add visualization support in `ui/viewport.py`

### Extending NLP Commands

Add new action types in `nlp/parser.py`:
```python
# In action schema
"new_action_type": {...}

# In parsing logic
elif action_type == "new_action_type":
    # Handle new action
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details

## Roadmap

### Phase 1 ✅ (Current)
- Basic 3D environment with heat simulation
- Rigid body physics with gravity and collisions
- Natural language interface with Gemini API
- Real-time interactive visualization
- Physics controls with gravity slider

### Phase 2 ✅ (Current)
- Fluid dynamics solver
- Neural surrogate model training
- Physics data generation pipeline
- 3D U-Net with physics constraints
- Complete ML training infrastructure

### Phase 2 (Next)
- Fluid dynamics solver
- Neural surrogate model training
- Data generation pipeline

### Phase 3 (Future)
- Rigid body physics
- Coupled multi-physics
- Advanced ML integration
- Blender addon version

## Support

For questions and support:
- Create an issue on GitHub
- Check the documentation in `docs/`
- Run `python test_basic.py` to verify installation