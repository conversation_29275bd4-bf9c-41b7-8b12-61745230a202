#!/usr/bin/env python3
"""
Integration test for NeoPhysics.

Tests the complete pipeline from natural language to physics simulation.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_basic_workflow():
    """Test basic natural language to simulation workflow."""
    print("Testing NeoPhysics integration...")
    
    # Create scene and executor
    scene = Scene("Test Scene")
    executor = ActionExecutor(scene)
    
    # Test commands
    commands = [
        "Place a hot cube at center with temperature 100",
        "Add a cold sphere at position 2,0,0 with temperature 0",
        "Create temperature field with grid size 32,32,32",
        "Run simulation for 50 steps"
    ]
    
    for i, command in enumerate(commands, 1):
        print(f"\n{i}. Command: '{command}'")
        
        try:
            # Parse command
            actions = parse_command(command)
            print(f"   Parsed {len(actions)} actions")
            
            # Execute actions
            for action in actions:
                result = executor.execute(action)
                if result["success"]:
                    print(f"   [OK] {result['message']}")
                else:
                    print(f"   [ERROR] {result['error']}")
        
        except Exception as e:
            print(f"   [ERROR] Error: {e}")
    
    # Print final scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Fields: {summary['num_fields']}")
    print(f"  Time: {summary['time']:.2f}s")
    
    print("\nIntegration test completed!")

if __name__ == "__main__":
    test_basic_workflow()