#!/usr/bin/env python3
"""
NeoPhysics - A Blender-like 3D Physics Sandbox with Natural Language Interface

Main entry point for the application.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.app import NeoPhysicsApp

def main():
    """Launch the NeoPhysics application."""
    print("Starting NeoPhysics...")
    print("A Blender-like 3D Physics Sandbox with Natural Language Interface")
    print("")
    
    # Check for Gemini API key
    gemini_key = os.getenv('GEMINI_API_KEY')
    if gemini_key and gemini_key != 'demo_mode':
        print("[INFO] Gemini API key found - advanced NLP enabled")
    else:
        print("[INFO] Running in demo mode - using rule-based parser")
        print("       Set GEMINI_API_KEY environment variable for advanced NLP")
    
    print("")
    
    try:
        app = NeoPhysicsApp()
        return app.run()
    except Exception as e:
        print(f"[ERROR] Failed to start application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())