#!/usr/bin/env python3
"""
Test script for the enhanced rigid body physics system.

Tests the new high-fidelity rigid body implementation with proper inertia tensors,
contact manifolds, and constraint-based solving.
"""

import numpy as np
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sim.rigid3d import (
    RigidBodySolver, RigidBody, BodyType, 
    SphereGeometry, BoxGeometry, InertiaTensor
)

def test_basic_sphere_creation():
    """Test creating spheres with the new system."""
    print("Testing sphere creation...")
    
    solver = RigidBodySolver()
    
    # Create a sphere using the new method
    sphere = solver.create_sphere_body(
        body_id="test_sphere",
        name="Test Sphere",
        position=np.array([0, 2, 0]),
        radius=0.5,
        mass=1.0,
        restitution=0.8
    )
    
    print(f"Created sphere: {sphere.name}")
    print(f"Position: {sphere.position}")
    print(f"Mass: {sphere.mass}")
    print(f"Inertia tensor: \n{sphere.inertia_tensor.tensor}")
    print(f"Geometry type: {sphere.collision_geometry.geometry_type}")
    print(f"Radius: {sphere.collision_geometry.radius}")
    
    return True

def test_basic_box_creation():
    """Test creating boxes with the new system."""
    print("\nTesting box creation...")
    
    solver = RigidBodySolver()
    
    # Create a box using the new method
    box = solver.create_box_body(
        body_id="test_box",
        name="Test Box",
        position=np.array([1, 3, 0]),
        dimensions=np.array([1.0, 0.5, 2.0]),
        mass=2.0,
        friction=0.5
    )
    
    print(f"Created box: {box.name}")
    print(f"Position: {box.position}")
    print(f"Mass: {box.mass}")
    print(f"Inertia tensor: \n{box.inertia_tensor.tensor}")
    print(f"Geometry type: {box.collision_geometry.geometry_type}")
    print(f"Half extents: {box.collision_geometry.half_extents}")
    
    return True

def test_simulation_step():
    """Test running simulation steps with the new system."""
    print("\nTesting simulation steps...")
    
    solver = RigidBodySolver(gravity=9.81)
    
    # Create two spheres
    sphere1 = solver.create_sphere_body(
        body_id="sphere1",
        name="Sphere 1",
        position=np.array([0, 3, 0]),
        radius=0.3,
        mass=1.0
    )
    
    sphere2 = solver.create_sphere_body(
        body_id="sphere2", 
        name="Sphere 2",
        position=np.array([0.5, 2, 0]),
        radius=0.2,
        mass=0.5
    )
    
    print("Initial state:")
    stats = solver.get_stats()
    print(f"Time: {stats['time']:.3f}")
    print(f"Total energy: {stats['total_energy']:.3f}")
    print(f"Bodies: {stats['num_bodies']}")
    print(f"Contacts: {stats['num_contacts']}")
    
    # Run simulation for a few steps
    for i in range(10):
        try:
            solver.step(dt=1.0/60.0)

            if i % 5 == 0:
                stats = solver.get_stats()
                print(f"\nStep {i}:")
                print(f"Time: {stats['time']:.3f}")
                print(f"Total energy: {stats['total_energy']:.3f}")
                print(f"Contacts: {stats['num_contacts']}")
                print(f"Sphere1 pos: {sphere1.position}")
                print(f"Sphere2 pos: {sphere2.position}")
        except Exception as e:
            print(f"Error at step {i}: {e}")
            import traceback
            traceback.print_exc()
            break
    
    return True

def test_inertia_tensor_calculations():
    """Test inertia tensor calculations."""
    print("\nTesting inertia tensor calculations...")
    
    # Test sphere inertia
    sphere_inertia = InertiaTensor.sphere(mass=2.0, radius=0.5)
    expected_sphere = (2.0/5.0) * 2.0 * 0.5**2
    print(f"Sphere inertia (expected {expected_sphere:.3f}): \n{sphere_inertia.tensor}")
    
    # Test box inertia
    box_inertia = InertiaTensor.box(mass=1.0, dimensions=np.array([2.0, 1.0, 0.5]))
    print(f"Box inertia: \n{box_inertia.tensor}")
    
    # Test inverse
    inverse = sphere_inertia.inverse()
    print(f"Sphere inertia inverse: \n{inverse}")
    
    return True

def test_rotation_matrix():
    """Test rotation matrix conversion."""
    print("\nTesting rotation matrix conversion...")
    
    solver = RigidBodySolver()
    sphere = solver.create_sphere_body(
        body_id="test_sphere",
        name="Test Sphere", 
        position=np.array([0, 0, 0]),
        radius=0.5
    )
    
    # Test identity quaternion
    rotation_matrix = sphere.get_rotation_matrix()
    print(f"Identity rotation matrix: \n{rotation_matrix}")
    
    # Test with some rotation
    sphere.rotation = np.array([0.707, 0.707, 0, 0])  # 90 degree rotation around X
    sphere.invalidate_cached_properties()
    rotation_matrix = sphere.get_rotation_matrix()
    print(f"90° X rotation matrix: \n{rotation_matrix}")
    
    return True

def main():
    """Run all tests."""
    print("Enhanced Rigid Body Physics System Tests")
    print("=" * 50)
    
    tests = [
        test_basic_sphere_creation,
        test_basic_box_creation,
        test_inertia_tensor_calculations,
        test_rotation_matrix,
        test_simulation_step,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("All tests passed! Enhanced rigid body system is working.")
        return 0
    else:
        print("Some tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
