"""
3D U-Net for Physics Field Prediction.

Neural surrogate model for scalar and vector field evolution.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional

class Conv3DBlock(nn.Module):
    """3D convolution block with normalization and activation."""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3):
        super().__init__()
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size, padding=1)
        self.norm1 = nn.GroupNorm(min(8, out_channels), out_channels)
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size, padding=1)
        self.norm2 = nn.GroupNorm(min(8, out_channels), out_channels)
        self.activation = nn.GELU()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.activation(self.norm1(self.conv1(x)))
        x = self.activation(self.norm2(self.conv2(x)))
        return x

class UNet3D(nn.Module):
    """3D U-Net for physics field prediction."""
    
    def __init__(self, 
                 in_channels: int = 4,  # current_field + boundary_mask + material_map + control
                 out_channels: int = 1,  # delta_field or next_field
                 base_channels: int = 16,
                 levels: int = 4):
        super().__init__()
        
        self.levels = levels
        
        # Encoder
        self.encoders = nn.ModuleList()
        self.pools = nn.ModuleList()
        
        channels = [in_channels] + [base_channels * (2**i) for i in range(levels)]
        
        for i in range(levels):
            self.encoders.append(Conv3DBlock(channels[i], channels[i+1]))
            if i < levels - 1:
                self.pools.append(nn.MaxPool3d(2))
        
        # Decoder
        self.decoders = nn.ModuleList()
        self.upsamples = nn.ModuleList()
        
        for i in range(levels - 1):
            self.upsamples.append(nn.ConvTranspose3d(
                channels[levels - i], channels[levels - i - 1], 
                kernel_size=2, stride=2
            ))
            self.decoders.append(Conv3DBlock(
                channels[levels - i - 1] * 2, channels[levels - i - 1]
            ))
        
        # Output layer
        self.output = nn.Conv3d(channels[1], out_channels, kernel_size=1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Encoder path
        encoder_outputs = []
        
        for i, encoder in enumerate(self.encoders):
            x = encoder(x)
            encoder_outputs.append(x)
            
            if i < len(self.pools):
                x = self.pools[i](x)
        
        # Decoder path
        for i, (upsample, decoder) in enumerate(zip(self.upsamples, self.decoders)):
            x = upsample(x)
            
            # Skip connection
            skip = encoder_outputs[-(i + 2)]
            
            # Handle size mismatch
            if x.shape != skip.shape:
                x = F.interpolate(x, size=skip.shape[2:], mode='trilinear', align_corners=False)
            
            x = torch.cat([x, skip], dim=1)
            x = decoder(x)
        
        return self.output(x)

class PhysicsUNet3D(nn.Module):
    """Physics-informed 3D U-Net with conservation constraints."""
    
    def __init__(self, 
                 in_channels: int = 4,
                 out_channels: int = 1,
                 base_channels: int = 16,
                 physics_type: str = "heat"):
        super().__init__()
        
        self.physics_type = physics_type
        self.unet = UNet3D(in_channels, out_channels, base_channels)
        
        # Physics constraint weights
        self.register_buffer('laplacian_weight', torch.tensor(0.1))
        self.register_buffer('conservation_weight', torch.tensor(1.0))
        self.register_buffer('boundary_weight', torch.tensor(10.0))
    
    def forward(self, x: torch.Tensor, 
                boundary_mask: Optional[torch.Tensor] = None,
                dt: float = 0.01) -> Tuple[torch.Tensor, dict]:
        """
        Forward pass with physics constraints.
        
        Args:
            x: Input tensor [B, C, D, H, W]
            boundary_mask: Boundary condition mask [B, 1, D, H, W]
            dt: Time step
            
        Returns:
            prediction: Predicted field
            losses: Dictionary of physics losses
        """
        # Get prediction
        pred = self.unet(x)
        
        # Physics losses
        losses = {}
        
        if self.physics_type == "heat":
            losses.update(self._heat_physics_losses(x, pred, boundary_mask, dt))
        elif self.physics_type == "fluid":
            losses.update(self._fluid_physics_losses(x, pred, boundary_mask, dt))
        
        return pred, losses
    
    def _heat_physics_losses(self, x: torch.Tensor, pred: torch.Tensor,
                           boundary_mask: Optional[torch.Tensor], dt: float) -> dict:
        """Compute heat equation physics losses."""
        losses = {}
        
        # Current temperature field (first channel)
        current_field = x[:, 0:1]
        
        # Laplacian loss (heat equation constraint)
        laplacian = self._compute_laplacian_3d(current_field)
        expected_change = laplacian * dt
        actual_change = pred
        
        losses['laplacian_loss'] = F.mse_loss(actual_change, expected_change) * self.laplacian_weight
        
        # Conservation loss
        total_before = torch.sum(current_field, dim=[2, 3, 4])
        total_after = torch.sum(current_field + pred, dim=[2, 3, 4])
        losses['conservation_loss'] = F.mse_loss(total_after, total_before) * self.conservation_weight
        
        # Boundary loss
        if boundary_mask is not None:
            boundary_violation = pred * boundary_mask
            losses['boundary_loss'] = torch.mean(boundary_violation**2) * self.boundary_weight
        
        return losses
    
    def _fluid_physics_losses(self, x: torch.Tensor, pred: torch.Tensor,
                            boundary_mask: Optional[torch.Tensor], dt: float) -> dict:
        """Compute fluid dynamics physics losses."""
        losses = {}
        
        # Divergence loss (incompressibility)
        if pred.shape[1] >= 3:  # Vector field
            div = self._compute_divergence_3d(pred[:, :3])
            losses['divergence_loss'] = torch.mean(div**2) * self.conservation_weight
        
        return losses
    
    def _compute_laplacian_3d(self, field: torch.Tensor) -> torch.Tensor:
        """Compute 3D Laplacian using finite differences."""
        # Pad for boundary conditions
        field_padded = F.pad(field, (1, 1, 1, 1, 1, 1), mode='replicate')
        
        # Finite difference stencil
        laplacian = (
            field_padded[:, :, 2:, 1:-1, 1:-1] + field_padded[:, :, :-2, 1:-1, 1:-1] +
            field_padded[:, :, 1:-1, 2:, 1:-1] + field_padded[:, :, 1:-1, :-2, 1:-1] +
            field_padded[:, :, 1:-1, 1:-1, 2:] + field_padded[:, :, 1:-1, 1:-1, :-2] -
            6 * field_padded[:, :, 1:-1, 1:-1, 1:-1]
        )
        
        return laplacian
    
    def _compute_divergence_3d(self, vector_field: torch.Tensor) -> torch.Tensor:
        """Compute 3D divergence of vector field."""
        u, v, w = vector_field[:, 0], vector_field[:, 1], vector_field[:, 2]
        
        # Finite differences
        du_dx = (u[:, :, :, 1:] - u[:, :, :, :-1])
        dv_dy = (v[:, :, 1:, :] - v[:, :, :-1, :])
        dw_dz = (w[:, 1:, :, :] - w[:, :-1, :, :])
        
        # Pad to match original size
        du_dx = F.pad(du_dx, (0, 1), mode='replicate')
        dv_dy = F.pad(dv_dy, (0, 0, 0, 1), mode='replicate')
        dw_dz = F.pad(dw_dz, (0, 0, 0, 0, 0, 1), mode='replicate')
        
        return du_dx + dv_dy + dw_dz

class ConvLSTM3DCell(nn.Module):
    """3D ConvLSTM cell for temporal modeling."""
    
    def __init__(self, input_channels: int, hidden_channels: int, kernel_size: int = 3):
        super().__init__()
        
        self.hidden_channels = hidden_channels
        padding = kernel_size // 2
        
        self.conv = nn.Conv3d(
            input_channels + hidden_channels, 
            4 * hidden_channels,
            kernel_size, 
            padding=padding
        )
    
    def forward(self, x: torch.Tensor, hidden: Tuple[torch.Tensor, torch.Tensor]):
        h, c = hidden
        
        combined = torch.cat([x, h], dim=1)
        gates = self.conv(combined)
        
        i, f, o, g = torch.split(gates, self.hidden_channels, dim=1)
        
        i = torch.sigmoid(i)
        f = torch.sigmoid(f)
        o = torch.sigmoid(o)
        g = torch.tanh(g)
        
        c_new = f * c + i * g
        h_new = o * torch.tanh(c_new)
        
        return h_new, (h_new, c_new)

class TemporalPhysicsNet(nn.Module):
    """Temporal physics network with ConvLSTM."""
    
    def __init__(self, 
                 input_channels: int = 4,
                 hidden_channels: int = 32,
                 output_channels: int = 1,
                 num_layers: int = 2):
        super().__init__()
        
        self.num_layers = num_layers
        self.hidden_channels = hidden_channels
        
        # ConvLSTM layers
        self.convlstm_layers = nn.ModuleList()
        
        for i in range(num_layers):
            in_ch = input_channels if i == 0 else hidden_channels
            self.convlstm_layers.append(
                ConvLSTM3DCell(in_ch, hidden_channels)
            )
        
        # Output layer
        self.output = nn.Conv3d(hidden_channels, output_channels, kernel_size=1)
    
    def forward(self, x: torch.Tensor, hidden_states: Optional[list] = None):
        """
        Forward pass through temporal network.
        
        Args:
            x: Input tensor [B, T, C, D, H, W]
            hidden_states: Previous hidden states
            
        Returns:
            output: Predicted sequence [B, T, C_out, D, H, W]
            new_hidden_states: Updated hidden states
        """
        batch_size, seq_len = x.shape[:2]
        
        if hidden_states is None:
            hidden_states = self._init_hidden(batch_size, x.shape[3:])
        
        outputs = []
        
        for t in range(seq_len):
            layer_input = x[:, t]
            
            for i, convlstm in enumerate(self.convlstm_layers):
                layer_input, hidden_states[i] = convlstm(layer_input, hidden_states[i])
            
            output = self.output(layer_input)
            outputs.append(output)
        
        return torch.stack(outputs, dim=1), hidden_states
    
    def _init_hidden(self, batch_size: int, spatial_shape: Tuple[int, int, int]):
        """Initialize hidden states."""
        device = next(self.parameters()).device
        hidden_states = []
        
        for _ in range(self.num_layers):
            h = torch.zeros(batch_size, self.hidden_channels, *spatial_shape, device=device)
            c = torch.zeros(batch_size, self.hidden_channels, *spatial_shape, device=device)
            hidden_states.append((h, c))
        
        return hidden_states