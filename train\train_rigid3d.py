"""
Training script for rigid body dynamics surrogate model.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import h5py
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import argparse
from tqdm import tqdm

class RigidBodyNet(nn.Module):
    """Neural network for rigid body dynamics prediction."""
    
    def __init__(self, input_dim: int = 9, hidden_dim: int = 128, output_dim: int = 6):
        """
        Args:
            input_dim: Input features per body (position + velocity + mass)
            hidden_dim: Hidden layer size
            output_dim: Output features per body (position + velocity deltas)
        """
        super().__init__()
        
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # Physics parameters
        self.register_buffer('dt', torch.tensor(1.0/60.0))
        self.register_buffer('gravity', torch.tensor(9.81))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            x: Input tensor [batch_size, num_bodies, input_dim]
            
        Returns:
            Delta positions and velocities [batch_size, num_bodies, output_dim]
        """
        batch_size, num_bodies, _ = x.shape
        
        # Flatten for processing
        x_flat = x.view(-1, x.shape[-1])
        
        # Neural network prediction
        delta_flat = self.net(x_flat)
        
        # Reshape back
        delta = delta_flat.view(batch_size, num_bodies, -1)
        
        return delta

class RigidBodyDataset(Dataset):
    """Dataset for rigid body dynamics training data."""
    
    def __init__(self, data_files: List[str], sequence_length: int = 2):
        self.data_files = data_files
        self.sequence_length = sequence_length
        self.samples = []
        
        # Index all valid sequences
        for file_path in data_files:
            with h5py.File(file_path, 'r') as f:
                metadata = json.loads(f.attrs['metadata'])
                num_timesteps = metadata['num_timesteps']
                
                for start_idx in range(num_timesteps - sequence_length + 1):
                    self.samples.append((file_path, start_idx))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        file_path, start_idx = self.samples[idx]
        
        with h5py.File(file_path, 'r') as f:
            # Get sequence of positions and velocities
            positions = f['positions'][start_idx:start_idx + self.sequence_length]
            velocities = f['velocities'][start_idx:start_idx + self.sequence_length]
            
            # Current state
            current_pos = positions[0]  # [num_bodies, 3]
            current_vel = velocities[0]  # [num_bodies, 3]
            
            # Next state
            next_pos = positions[1]
            next_vel = velocities[1]
            
            # Get metadata for masses (simplified - assume unit mass)
            num_bodies = current_pos.shape[0]
            masses = np.ones((num_bodies, 1))
            
            # Pad to fixed size (max 5 bodies)
            max_bodies = 5
            if num_bodies > max_bodies:
                # Truncate if too many bodies
                current_pos = current_pos[:max_bodies]
                current_vel = current_vel[:max_bodies]
                next_pos = next_pos[:max_bodies]
                next_vel = next_vel[:max_bodies]
                masses = masses[:max_bodies]
                num_bodies = max_bodies
            
            # Input: position + velocity + mass
            input_features = np.concatenate([
                current_pos,  # [num_bodies, 3]
                current_vel,  # [num_bodies, 3]
                masses       # [num_bodies, 1]
            ], axis=1)  # [num_bodies, 7]
            
            # Target: delta position + delta velocity
            delta_pos = next_pos - current_pos
            delta_vel = next_vel - current_vel
            target = np.concatenate([delta_pos, delta_vel], axis=1)  # [num_bodies, 6]
            
            # Pad with zeros if needed
            if num_bodies < max_bodies:
                pad_size = max_bodies - num_bodies
                input_pad = np.zeros((pad_size, 7))
                target_pad = np.zeros((pad_size, 6))
                
                input_features = np.concatenate([input_features, input_pad], axis=0)
                target = np.concatenate([target, target_pad], axis=0)
            
            return {
                'input': torch.FloatTensor(input_features),
                'target': torch.FloatTensor(target)
            }

def train_epoch(model: nn.Module, dataloader: DataLoader, optimizer: optim.Optimizer,
                device: torch.device) -> Dict[str, float]:
    """Train for one epoch."""
    model.train()
    total_loss = 0
    
    for batch in tqdm(dataloader, desc="Training"):
        optimizer.zero_grad()
        
        inputs = batch['input'].to(device)  # [batch_size, num_bodies, input_dim]
        targets = batch['target'].to(device)  # [batch_size, num_bodies, output_dim]
        
        # Forward pass
        predictions = model(inputs)
        
        # MSE loss
        loss = nn.MSELoss()(predictions, targets)
        
        # Backward pass
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
    
    return {'total_loss': total_loss / len(dataloader)}

def validate(model: nn.Module, dataloader: DataLoader, device: torch.device) -> Dict[str, float]:
    """Validate model."""
    model.eval()
    total_loss = 0
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Validation"):
            inputs = batch['input'].to(device)
            targets = batch['target'].to(device)
            
            predictions = model(inputs)
            loss = nn.MSELoss()(predictions, targets)
            total_loss += loss.item()
    
    return {'val_loss': total_loss / len(dataloader)}

def main():
    parser = argparse.ArgumentParser(description='Train Rigid Body Dynamics Model')
    parser.add_argument('--data_dir', type=str, required=True, help='Directory with training data')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='checkpoints', help='Output directory')
    
    args = parser.parse_args()
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Find data files
    data_dir = Path(args.data_dir)
    data_files = list(data_dir.glob("rigid_*.h5"))
    print(f"Found {len(data_files)} data files")
    
    if len(data_files) == 0:
        print("No rigid body data files found! Generate data first.")
        return
    
    # Split data
    train_files = data_files[:int(0.8 * len(data_files))]
    val_files = data_files[int(0.8 * len(data_files)):]
    
    # Create datasets
    train_dataset = RigidBodyDataset(train_files)
    val_dataset = RigidBodyDataset(val_files)
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Create model
    model = RigidBodyNet(
        input_dim=7,  # pos(3) + vel(3) + mass(1)
        hidden_dim=128,
        output_dim=6   # delta_pos(3) + delta_vel(3)
    ).to(device)
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # Training loop
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        print(f"\nEpoch {epoch + 1}/{args.epochs}")
        
        # Train
        train_metrics = train_epoch(model, train_loader, optimizer, device)
        
        # Validate
        val_metrics = validate(model, val_loader, device)
        
        # Update scheduler
        scheduler.step(val_metrics['val_loss'])
        
        # Print metrics
        print(f"Train Loss: {train_metrics['total_loss']:.6f}")
        print(f"Val Loss: {val_metrics['val_loss']:.6f}")
        print(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # Save best model
        if val_metrics['val_loss'] < best_val_loss:
            best_val_loss = val_metrics['val_loss']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': best_val_loss,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics
            }, output_dir / 'best_rigid_model.pth')
            print("Saved best model!")
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
            }, output_dir / f'rigid_checkpoint_epoch_{epoch + 1}.pth')
    
    print(f"\nTraining completed! Best validation loss: {best_val_loss:.6f}")

if __name__ == "__main__":
    main()